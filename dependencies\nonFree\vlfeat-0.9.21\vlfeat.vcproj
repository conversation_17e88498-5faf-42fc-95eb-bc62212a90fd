<?xml version="1.0" encoding="Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="9.00"
	Name="vlfeat"
	ProjectGUID="{07367665-DCDA-4044-94CE-ABC01CC4119D}"
	RootNamespace="vlfeat"
	Keyword="MakeFileProj"
	TargetFrameworkVersion="196613"
	>
	<Platforms>
		<Platform
			Name="Win32"
		/>
	</Platforms>
	<ToolFiles>
	</ToolFiles>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="vc\$(ConfigurationName)"
			IntermediateDirectory="vc\$(ConfigurationName)"
			ConfigurationType="0"
			BuildLogFile="vc\BuildLog.htm"
			>
			<Tool
				Name="VCNMakeTool"
				BuildCommandLine="set VCInstallDir=$(VCInstallDir)&#x0D;&#x0A;set WindowsSdkDir=$(WindowsSdkDir)&#x0D;&#x0A;nmake /f Makefile.mak DEBUG=yes&#x0D;&#x0A;"
				ReBuildCommandLine="set VCInstallDir=$(VCInstallDir)&#x0D;&#x0A;set WindowsSdkDir=$(WindowsSdkDir)&#x0D;&#x0A;nmake /f Makefile.mak distclean all DEBUG=yesl"
				CleanCommandLine="set VCInstallDir=$(VCInstallDir)&#x0D;&#x0A;set WindowsSdkDir=$(WindowsSdkDir)&#x0D;&#x0A;nmake /f Makefile.mak  clean"
				Output=""
				PreprocessorDefinitions="WIN32;_DEBUG"
				IncludeSearchPath=""
				ForcedIncludes=""
				AssemblySearchPath=""
				ForcedUsingAssemblies=""
				CompileAsManaged=""
			/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="$(ConfigurationName)"
			IntermediateDirectory="$(ConfigurationName)"
			ConfigurationType="0"
			>
			<Tool
				Name="VCNMakeTool"
				BuildCommandLine="nmake /f Makefile.mak"
				ReBuildCommandLine="nmake /f Makefile.mak clean all"
				CleanCommandLine="nmake /f Makefile.mak clean"
				Output=""
				PreprocessorDefinitions="WIN32;NDEBUG"
				IncludeSearchPath=""
				ForcedIncludes=""
				AssemblySearchPath=""
				ForcedUsingAssemblies=""
				CompileAsManaged=""
			/>
		</Configuration>
	</Configurations>
	<References>
	</References>
	<Files>
		<Filter
			Name="src"
			Filter="cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx"
			UniqueIdentifier="{4FC737F1-C7A5-4376-A066-2A32D752A2FF}"
			>
			<File
				RelativePath=".\src\aib.c"
				>
			</File>
			<File
				RelativePath=".\src\generic-driver.h"
				>
			</File>
			<File
				RelativePath=".\src\mser.c"
				>
			</File>
			<File
				RelativePath=".\src\sift.c"
				>
			</File>
			<File
				RelativePath=".\src\test_getopt_long.c"
				>
			</File>
			<File
				RelativePath=".\src\test_heap-t.c"
				>
			</File>
			<File
				RelativePath=".\src\test_heap.c"
				>
			</File>
			<File
				RelativePath=".\src\test_host.c"
				>
			</File>
			<File
				RelativePath=".\src\test_imopv.c"
				>
			</File>
			<File
				RelativePath=".\src\test_mathop.c"
				>
			</File>
			<File
				RelativePath=".\src\test_mathop_abs.c"
				>
			</File>
			<File
				RelativePath=".\src\test_nan.c"
				>
			</File>
			<File
				RelativePath=".\src\test_rand.c"
				>
			</File>
			<File
				RelativePath=".\src\test_stringop.c"
				>
			</File>
			<File
				RelativePath=".\src\test_threads.c"
				>
			</File>
			<File
				RelativePath=".\src\test_vec_comp.c"
				>
			</File>
		</Filter>
		<Filter
			Name="vl"
			Filter="h;hpp;hxx;hm;inl;inc;xsd"
			UniqueIdentifier="{93995380-89BD-4b04-88EB-625FBE52EBFB}"
			>
			<File
				RelativePath=".\vl\aib.c"
				>
			</File>
			<File
				RelativePath=".\vl\aib.h"
				>
			</File>
			<File
				RelativePath=".\vl\array.c"
				>
			</File>
			<File
				RelativePath=".\vl\array.h"
				>
			</File>
			<File
				RelativePath=".\vl\covdet.c"
				>
			</File>
			<File
				RelativePath=".\vl\covdet.h"
				>
			</File>
			<File
				RelativePath=".\vl\dsift.c"
				>
			</File>
			<File
				RelativePath=".\vl\dsift.h"
				>
			</File>
			<File
				RelativePath=".\vl\float.th"
				>
			</File>
			<File
				RelativePath=".\vl\generic.c"
				>
			</File>
			<File
				RelativePath=".\vl\generic.h"
				>
			</File>
			<File
				RelativePath=".\vl\getopt_long.c"
				>
			</File>
			<File
				RelativePath=".\vl\getopt_long.h"
				>
			</File>
			<File
				RelativePath=".\vl\gmm.c"
				>
			</File>
			<File
				RelativePath=".\vl\gmm.h"
				>
			</File>
			<File
				RelativePath=".\vl\heap-def.h"
				>
			</File>
			<File
				RelativePath=".\vl\hikmeans.c"
				>
			</File>
			<File
				RelativePath=".\vl\hikmeans.h"
				>
			</File>
			<File
				RelativePath=".\vl\hog.c"
				>
			</File>
			<File
				RelativePath=".\vl\hog.h"
				>
			</File>
			<File
				RelativePath=".\vl\homkermap.c"
				>
			</File>
			<File
				RelativePath=".\vl\homkermap.h"
				>
			</File>
			<File
				RelativePath=".\vl\host.c"
				>
			</File>
			<File
				RelativePath=".\vl\host.h"
				>
			</File>
			<File
				RelativePath=".\vl\ikmeans.c"
				>
			</File>
			<File
				RelativePath=".\vl\ikmeans.h"
				>
			</File>
			<File
				RelativePath=".\vl\ikmeans_elkan.tc"
				>
			</File>
			<File
				RelativePath=".\vl\ikmeans_init.tc"
				>
			</File>
			<File
				RelativePath=".\vl\ikmeans_lloyd.tc"
				>
			</File>
			<File
				RelativePath=".\vl\imopv.c"
				>
			</File>
			<File
				RelativePath=".\vl\imopv.h"
				>
			</File>
			<File
				RelativePath=".\vl\imopv_sse2.c"
				>
			</File>
			<File
				RelativePath=".\vl\imopv_sse2.h"
				>
			</File>
			<File
				RelativePath=".\vl\kdtree.c"
				>
			</File>
			<File
				RelativePath=".\vl\kdtree.h"
				>
			</File>
			<File
				RelativePath=".\vl\kmeans.c"
				>
			</File>
			<File
				RelativePath=".\vl\kmeans.h"
				>
			</File>
			<File
				RelativePath=".\vl\lbp.c"
				>
			</File>
			<File
				RelativePath=".\vl\lbp.h"
				>
			</File>
			<File
				RelativePath=".\vl\liop.c"
				>
			</File>
			<File
				RelativePath=".\vl\liop.h"
				>
			</File>
			<File
				RelativePath=".\vl\mathop.c"
				>
			</File>
			<File
				RelativePath=".\vl\mathop.h"
				>
			</File>
			<File
				RelativePath=".\vl\mathop_avx.c"
				>
			</File>
			<File
				RelativePath=".\vl\mathop_avx.h"
				>
			</File>
			<File
				RelativePath=".\vl\mathop_sse2.c"
				>
			</File>
			<File
				RelativePath=".\vl\mathop_sse2.h"
				>
			</File>
			<File
				RelativePath=".\vl\mser.c"
				>
			</File>
			<File
				RelativePath=".\vl\mser.h"
				>
			</File>
			<File
				RelativePath=".\vl\pegasos.c"
				>
			</File>
			<File
				RelativePath=".\vl\pegasos.h"
				>
			</File>
			<File
				RelativePath=".\vl\pgm.c"
				>
			</File>
			<File
				RelativePath=".\vl\pgm.h"
				>
			</File>
			<File
				RelativePath=".\vl\qsort-def.h"
				>
			</File>
			<File
				RelativePath=".\vl\quickshift.c"
				>
			</File>
			<File
				RelativePath=".\vl\quickshift.h"
				>
			</File>
			<File
				RelativePath=".\vl\random.c"
				>
			</File>
			<File
				RelativePath=".\vl\random.h"
				>
			</File>
			<File
				RelativePath=".\vl\rodrigues.c"
				>
			</File>
			<File
				RelativePath=".\vl\rodrigues.h"
				>
			</File>
			<File
				RelativePath=".\vl\scalespace.c"
				>
			</File>
			<File
				RelativePath=".\vl\scalespace.h"
				>
			</File>
			<File
				RelativePath=".\vl\shuffle-def.h"
				>
			</File>
			<File
				RelativePath=".\vl\sift.c"
				>
			</File>
			<File
				RelativePath=".\vl\sift.h"
				>
			</File>
			<File
				RelativePath=".\vl\slic.c"
				>
			</File>
			<File
				RelativePath=".\vl\slic.h"
				>
			</File>
			<File
				RelativePath=".\vl\stringop.c"
				>
			</File>
			<File
				RelativePath=".\vl\stringop.h"
				>
			</File>
			<File
				RelativePath=".\vl\svmdataset.c"
				>
			</File>
			<File
				RelativePath=".\vl\svmdataset.h"
				>
			</File>
		</Filter>
		<Filter
			Name="toolbox"
			>
			<File
				RelativePath=".\toolbox\misc\kdtree.h"
				>
			</File>
			<File
				RelativePath=".\toolbox\mexutils.h"
				>
			</File>
			<File
				RelativePath=".\toolbox\aib\vl_aib.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\aib\vl_aibhist.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\misc\vl_alldist.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\misc\vl_alldist2.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\misc\vl_binsearch.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\misc\vl_binsum.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\misc\vl_cummax.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\sift\vl_dsift.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\mser\vl_erfill.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\misc\vl_getpid.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\gmm\vl_gmm.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\kmeans\vl_hikmeans.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\kmeans\vl_hikmeanspush.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\misc\vl_hog.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\misc\vl_homkermap.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\misc\vl_ihashfind.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\misc\vl_ihashsum.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\kmeans\vl_ikmeans.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\kmeans\vl_ikmeanspush.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\imop\vl_imdisttf.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\imop\vl_imintegral.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\imop\vl_imsmooth.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\imop\vl_imwbackwardmx.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\misc\vl_inthist.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\geometry\vl_irodr.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\misc\vl_kdtreebuild.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\misc\vl_kdtreequery.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\kmeans\vl_kmeans.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\misc\vl_lbp.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\misc\vl_localmax.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\misc\vl_maketrainingset.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\mser\vl_mser.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\misc\vl_pegasos.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\quickshift\vl_quickshift.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\geometry\vl_rodr.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\misc\vl_sampleinthist.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\misc\vl_samplinthist.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\sift\vl_sift.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\sift\vl_siftdescriptor.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\misc\vl_simdctrl.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\slic\vl_slic.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\misc\vl_svmpegasos.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\imop\vl_tpsumx.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\misc\vl_twister.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\sift\vl_ubcmatch.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\misc\vl_version.c"
				>
			</File>
			<File
				RelativePath=".\toolbox\misc\vl_whistc.c"
				>
			</File>
		</Filter>
		<Filter
			Name="make"
			>
			<File
				RelativePath=".\make\bin.mak"
				>
			</File>
			<File
				RelativePath=".\make\dist.mak"
				>
			</File>
			<File
				RelativePath=".\make\dll.mak"
				>
			</File>
			<File
				RelativePath=".\make\doc.mak"
				>
			</File>
			<File
				RelativePath=".\make\matlab.mak"
				>
			</File>
			<File
				RelativePath=".\make\octave.mak"
				>
			</File>
			<File
				RelativePath=".\make\update-mak.sh"
				>
			</File>
		</Filter>
		<File
			RelativePath=".\Makefile.mak"
			>
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
