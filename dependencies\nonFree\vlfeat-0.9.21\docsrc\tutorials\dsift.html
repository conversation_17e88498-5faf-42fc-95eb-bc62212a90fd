<!DOCTYPE group PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<group>

%tableofcontents;

<p>VLFeat implements a <a href="%dox:dsift;">fast dense version of
SIFT</a>, called
<code>vl_dsift</code>. The function is roughly equivalent to running
SIFT on a dense gird of locations at a fixed scale and
orientation. This type of feature descriptors is often uses for object
categorization.</p>

<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
<h1 id="tut.dsift.fast">Dense SIFT as a faster SIFT</h1>
<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->

<p>The main advantage of using <code>vl_dsift</code>
over <code>vl_sift</code> is speed. To see this, load a test image</p>

<precode type='matlab'>
I = vl_impattern('roofs1') ;
I = single(vl_imdown(rgb2gray(I))) ;
</precode>

<p>To check the equivalence of <code>vl_disft</code>
and <code>vl_sift</code> it is necessary to understand in detail how
the parameters of the two descriptors are related.</p>

<ul>
<li><p><b>Bin size vs keypoint scale</b>. DSIFT specifies the
descriptor size by a single parameter, <code>size</code>, which controls
the size of a SIFT spatial bin in pixels. In the standard SIFT
descriptor, the bin size is related to the SIFT keypoint scale by a
multiplier, denoted <code>magnif</code> below, which defaults
to <code>3</code>. As a consequence, a DSIFT descriptor with bin size
equal to 5 corresponds to a SIFT keypoint of scale 5/3=1.66.</p></li>

<li><p><b>Smoothing.</b> The SIFT descriptor smoothes the image
according to the scale of the keypoints (Gaussian scale space). By
default, the smoothing is equivalent to a convolution by a Gaussian of
variance <code>s^2 - .25</code>, where <code>s</code> is the scale of
the keypoint and <code>.25</code> is a nominal adjustment that
accounts for the smoothing induced by the camera CCD.</p></li>
</ul>

<p>Thus the following code produces equivalent descriptors using
  either DSIFT or SIFT:</p>

<precode type='matlab'>
binSize = 8 ;
magnif = 3 ;
Is = vl_imsmooth(I, sqrt((binSize/magnif)^2 - .25)) ;

[f, d] = vl_dsift(Is, 'size', binSize) ;
f(3,:) = binSize/magnif ;
f(4,:) = 0 ;
[f_, d_] = vl_sift(I, 'frames', f) ;
</precode>

<p>The difference, of course, is that DSIFT is much faster.</p>

<div class="figure">
 <img src="%pathto:root;demo/dsift_accuracy.jpg"/>
 <img src="%pathto:root;demo/dsift_speedup.jpg"/>
 <div class="caption">
  <span class="content">
    Left: accuracy of the slow and fast dense SIFT implementations in
    <code>vl_dsift</code> compared to the SIFT baseline from <code>
    vl_sift</code>. Right: speedup. The fast version is less similar
    to the original SIFT descriptors but from 30 to 70 times faster
    than SIFT. Notice that the equivalence of the descriptors does not
    necessarily indicate that one would work better than the other in
    applications.
  </span>
 </div>
</div>

<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
<h1 id="tut.dsift.phow">PHOW descriptors</h1>
<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->

<p>The PHOW features <a href="#ref1">[1]</a> are a variant of dense
SIFT descriptors, extracted at multiple scales. A color version, named
PHOW-color, extracts descriptors on the three HSV image channels and
stacks them up. A combination of <code>vl_dsift</code>
and <code>vl_imsmooth</code> can be used to easily and efficiently
compute such features.</p>

<p>VLFeat includes a simple wrapper, <code>vl_phow</code>, that does
exactly this:</p>

<precode type='matlab'>
im = vl_impattern('roofs1') ;
[frames, descrs]=vl_phow(im2single(im)) ;
</precode>

<p>Note that this typically generate a very large number of features.
In this example, there are 162,574 features.</p>

<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
<h1 id="tut.dsift.references">References</h1>
<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->

<ul>
  <li id="ref1">[1] A. Bosch, A. Zisserman, and X. Munoz. <em>Image
  classifcation using random forests and ferns.</em> In Proc. ICCV,
  2007.
</li>
</ul>

</group>
