/*
* @Author: 王仁华
* @Date: 2023-08-22
* @LastEditors:王仁华
* @LastEditTime:2023-08-22
* @Version:1.0.0
* @Description:SWDC
*/
#pragma once

#include "IStitch.hpp"
namespace SWDC {
	namespace stitch {
		class IStitch_cpu : public IStitch
		{
		public:
			IStitch_cpu(const data::Data& data, const HashMap<stitch::ImagePair, std::string>& outputMap) :IStitch(data, outputMap)
			{

			}

			bool process(std::function<void(double progress, std::string message)> func = nullptr) override { return true; };

			bool useGPU() const {
				return false;
			};

		protected:
			void genCorrectionImage(const image::Image<Vec3c>& image, const std::shared_ptr<camera::IntrinsicBase> camera, const data::Pose3& pose, image::Image<Vec3c>* imageout) override;

			void genOverlapRegionFeature(const image::Image<Vec3c>& image, const std::shared_ptr<camera::IntrinsicBase> camera, const data::Pose3& pose, const Quadrangle& overlapRectangle, const std::shared_ptr<feature::ImageDescriber> imageDescriber, std::unique_ptr<feature::Regions>& regions) override;
		};
	}
}


