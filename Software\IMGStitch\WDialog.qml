import QtQuick 2.12
import QtQuick.Controls 2.14
import QtQuick.Dialogs 1.3

Dialog {
    property string text: ""
    property string imgSource: ""
    signal clickSureButton()

    id: dialog
    height: 70
    width: 200
    modality: Qt.WindowModal
    contentItem:Rectangle{
        id:message
        anchors.fill: parent
        Image {
            id:iconImage
            source: imgSource
            width: 32
            height: 32
            anchors.top: parent.top
            anchors.left: parent.left
            anchors.leftMargin: 10
            anchors.topMargin: 8
        }
        Text {
            id: messageText
            text: dialog.text
            opacity: 1
            anchors.top: parent.top
            anchors.bottom: parent.bottom
            anchors.left: iconImage.right
            anchors.leftMargin: 10
            anchors.bottomMargin: 20
            verticalAlignment: Text.AlignVCenter
            horizontalAlignment: Text.AlignHCenter
            font.pixelSize: 12
            font.family: "Verdana"
            color: "#000"
        }

        WButton{
            text:"确定"
            font.family: "Verdana"
            width: 50
            height: 20
            anchors.right:parent.right
            anchors.bottom: parent.bottom
            anchors.rightMargin: 10
            anchors.bottomMargin: 10
            onClickButton: clickSureButton()
        }
    }
}

