<!DOCTYPE site PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
 <head>
  <!-- IE Standards Mode -->
  <meta http-equiv="X-UA-Compatible" content="IE=edge"/>

  <!-- Favicon -->
  <link rel="icon" href="%pathto:root;images/vl_blue.ico" type="image/x-icon"/>
  <link rel="shortcut icon" href="%pathto:root;images/vl_blue.ico" type="image/x-icon"/>

  <!-- Page title -->
  <title>VLFeat - %path:plain;</title>

  <!-- Stylesheets -->
  <link href="%pathto:root;vlfeat.css" rel="stylesheet" type="text/css"/>
  <link href="%pathto:root;pygmentize.css" rel="stylesheet" type="text/css"/>
  <style>
    /* fixes a conflict between Pygmentize and MathJax */
    .MathJax .mo, .MathJax .mi {color: inherit ! important}
  </style>
  %pagestyle;

  <!-- Scripts-->
  %pagescript;

  <!-- MathJax -->
  <script type="text/x-mathjax-config">
    MathJax.Hub.Config({
    tex2jax: {
      inlineMath: [ ['$','$'], ['\\(','\\)'] ],
      processEscapes: true,
    },
    TeX: {
      Macros: {
        balpha: '\\boldsymbol{\\alpha}',
        bc: '\\mathbf{c}',
        be: '\\mathbf{e}',
        bg: '\\mathbf{g}',
        bq: '\\mathbf{q}',
        bu: '\\mathbf{u}',
        bv: '\\mathbf{v}',
        bw: '\\mathbf{w}',
        bx: '\\mathbf{x}',
        by: '\\mathbf{y}',
        bz: '\\mathbf{z}',
        bsigma: '\\mathbf{\\sigma}',
        sign: '\\operatorname{sign}',
        diag: '\\operatorname{diag}',
        real: '\\mathbb{R}',
      },
      equationNumbers: { autoNumber: 'AMS' }
      }
    });
  </script>
  <script type="text/javascript"
          src="http://cdn.mathjax.org/mathjax/latest/MathJax.js?config=TeX-AMS-MML_HTMLorMML"/>

  <!-- Google Custom Search -->
  <script>
    (function() {
    var cx = '003215582122030917471:oq23albfeam';
    var gcse = document.createElement('script'); gcse.type = 'text/javascript'; gcse.async = true;
    gcse.src = (document.location.protocol == 'https' ? 'https:' : 'http:') +
    '//www.google.com/cse/cse.js?cx=' + cx;
    var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(gcse, s);
    })();
  </script>

  <!-- Google Analytics -->
  <script type="text/javascript">
    var _gaq = _gaq || [];
    _gaq.push(['_setAccount', 'UA-4936091-2']);
    _gaq.push(['_trackPageview']);
    (function() {
    var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
    ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
    var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
    })();
  </script>
 </head>

 <!-- Body Start -->
 <body>
  <div id="header-section">
    <div id="header">
      <!-- Google CSE Search Box -->
      <div class="searchbox">
        <gcse:searchbox-only resultsUrl="http://www.vlfeat.org/search.html"
                             autoCompleteMaxCompletions="5"
                             autoCompleteMatchType="any"/>
      </div>
      <h1><a class="plain" href="%pathto:home;"><span id="vlfeat">VLFeat</span><span id="dotorg">.org</span></a></h1>
    </div>
    <div id="sidebar"> <!-- Navigation Start -->
      %navigation;
    </div> <!-- sidebar -->
  </div>
  <div id="headbanner-section">
    <div id="headbanner">
      %path;
    </div>
  </div>
  <div id="content-section">
    <div id="content-wrapper">
      <div id="content">
        %content;
      </div>
      <div class="clear">&nbsp;</div>
    </div>
  </div> <!-- content-section -->
  <div id="footer-section">
    <div id="footer">
      &copy; 2007-14,18 The VLFeat Authors
    </div> <!-- footer -->
  </div> <!-- footer section -->
 </body>
 <!-- Body ends -->
</html>
