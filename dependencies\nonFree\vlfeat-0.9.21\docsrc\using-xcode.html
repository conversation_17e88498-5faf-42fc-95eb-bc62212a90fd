<!DOCTYPE group PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<group>

 <p>These instructions show how to setup a basic <b>VLFeat</b> project
  with Apple Xcode.  For the sake of simplicty, we create a command
  line tool written in C. However, these steps apply with minor
  modifications to other project types and to the C++ lanuage.</p>

 <p>First, let us create a new project
  called <code>vlfeat-client</code>. Open Xcode and select <b>File
  &gt; New Project &gt; Command Line Utility &gt; Standard Tool</b>
  and click <b>Choose</b>. Give a name to your project (in our
  case <code>vlfeat-client</code>), and click <b>Save</b>.</p>

 <div class="figure">
 <img alt="Xcode new project" src="%pathto:root;images/using-xcode-new.png"/>
 </div>

 <p>Now we need to add <b>VLFeat</b> to the C compiler include search
  path. To do this, select the <code>vlfeat-client</code> target and
  open the information panel (the blue button,
  or <b>Command-i</b>). Then select the <b>Build</b> panel, search for
  the field <b>Header Search Paths</b>, and add
  <b>VLFeat</b> root path (in our case this is
  just <code>~/src/vlfeat</code>).</p>

 <img alt="Xcode info" src="%pathto:root;images/using-xcode-info.png"/>

 <p>Next, we add the <code>libvl.dylib</code> library file to the
  project resources so that Xcode links against it. To do this, drag
  and drop the <code>libvl.dylib</code> file (in our example
  <code>~/src/vlfeat/bin/maci/libvl.dylib</code>) to the left panel and click
  <b>Add</b>.</p>

 <img alt="Xcode dylib" src="%pathto:root;images/using-xcode-dylib.png"/>

 <p>Next, edit the <code>main.c</code> source file and type the following code:</p>

<precode type="c">
#include &lt;vl/generic.h&gt;

int main (int argc, const char * argv[]) {
  VL_PRINT ("Hello world!") ;
  return 0;
}
</precode>

 <img alt="Xcode edit" src="%pathto:root;images/using-xcode-edit.png"/>

 <p>If you try to build the project, it should compile without errors
  (if you are using C++, do not forget to wrap the <code>include</code>
  statements in a <code>extern "C" {}</code> block). However, if you try
  to run the program, it will fail, complaining that it cannot find the
  library image.</p>

 <img alt="Xcode error" src="%pathto:root;images/using-xcode-err.png"/>

 <p>The reason is that <code>libvl.dylib</code> is compiled with the
  library <code>install_name</code> equal
  to <code>@loader_path/libvl.dylib</code>. This causes the run-time
  loader to look for the library in the same directory of the
  executable. There are two ways around this problem: The first is to
  install the library in a standard location
  (e.g. <code>/usr/local/lib</code>) and use the <code>otool</code>
  command to change the
  library <code>install_name</code>. The other is to simply copy
  the <code>libvl.dylib</code> file in the executable directory. Here we
  demonstrate the second technique.</p>

 <p>To copy <code>libvl.dylib</code> in the executable directory, we
  add a <b>Copy Files</b> build phase to the project. Right-click
  the <code>vlfeat-client</code> target in the project panel and select
  <b>Add &gt; New Build Phase &gt; New Copy Files Build
   Phase</b>. Select <b>Destination: Executables</b>. Then drag-and-drop
  the <code>libvl.dylib</code> item from the panel to the <b>Copy
   Files</b> build phase.</p>

 <img alt="Xcode copy" src="%pathto:root;images/using-xcode-copy.png"/>
 <img alt="Xcode copy" src="%pathto:root;images/using-xcode-copy-2.png"/>

 <p>Now rebuild the project, and run it. It should run correctly,
  and if you open the debugger console you should see this:</p>

 <img alt="Xcode ok" src="%pathto:root;images/using-xcode-ok.png"/>

</group>
