#pragma once
#include <image.hpp>
#include <PinholeBrown.hpp>
#include <ImageDescriber.hpp>
#include <iostream>
#include <numeric.hpp>

namespace SWDC {
	namespace stitch {

		struct Quadrangle
		{
			Vec3 leftTop;
			Vec3 rightTop;
			Vec3 rightBottom;
			Vec3 leftBottom;

			Quadrangle() {};
			Quadrangle(Vec3 LT, Vec3 RT, Vec3 RB, Vec3 LB): leftTop(LT), rightTop(RT), rightBottom(RB), leftBottom(LB){};

			friend std::ostream& operator<<(std::ostream& os, const Quadrangle& T);
		};

		enum class ETnterpolation {
			NEAREST,
			BILINEAR
		};

		class Stitch
		{
		public:
			Stitch(double virtualPixSize, double virtualFocalLength, double averageHeight, unsigned int virtualWidth, unsigned int virtualHeight) :
				_virtualPixSize(virtualPixSize), _virtualFocalLength(virtualFocalLength),
				_averageHeight(averageHeight),
				_virtualWidth(virtualWidth), _virtualHeight(virtualHeight)
			{
				_groundSampleSize = fabs(virtualPixSize * averageHeight / virtualFocalLength);
			}

			void setTnterpolation(ETnterpolation method) {
				_tnterpolation = method;
			}

			Quadrangle getGroundQuadrangle(const std::shared_ptr<camera::IntrinsicBase> camera, const data::Pose3 &pose);

			Quadrangle getPolygonBoundingRectangle(const Quadrangle &quadrangle1, const Quadrangle& quadrangle2);

			void genCorrectionImage(const image::Image<float>& image,const std::shared_ptr<camera::IntrinsicBase> camera, const data::Pose3 &pose, image::Image<float>* imgout);

			void genCorrectionImage(const image::Image<Vec3f>& image, const std::shared_ptr<camera::IntrinsicBase> camera, const data::Pose3& pose, image::Image<Vec3f>* imgout);

			void genOverlapRegionFeature(const image::Image<float>& image, const std::shared_ptr<camera::IntrinsicBase> camera, const data::Pose3& pose, const Quadrangle &overlapRectangle, const std::shared_ptr<feature::ImageDescriber> imageDescriber, std::unique_ptr<feature::Regions>& regions);

			void genOverlapRegionFeature(const image::Image<Vec3f>& image, const std::shared_ptr<camera::IntrinsicBase> camera, const data::Pose3& pose, const Quadrangle& overlapRectangle, const std::shared_ptr<feature::ImageDescriber> imageDescriber, std::unique_ptr<feature::Regions>& regions);

			void warpPerspectiveAndStitching(const image::Image<float>& image1, const image::Image<float>& image2,const Mat3&  H, image::Image<float>* imageout);

			void warpPerspectiveAndStitching(const image::Image<Vec3f>& image1, const image::Image<Vec3f>& image2, const Mat3& H, image::Image<Vec3f>* imageout);
			
			~Stitch() {};
		private:
			double _virtualPixSize;
			double _averageHeight;
			double _virtualFocalLength;
			double _groundSampleSize;

			unsigned int _virtualWidth;
			unsigned int _virtualHeight;

			ETnterpolation _tnterpolation = ETnterpolation::NEAREST;
		};
	}
}