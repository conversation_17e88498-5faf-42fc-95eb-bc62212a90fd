/*
* @Author: 王仁华
* @Date: 2023-08-22
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-11-22 11:01:17
* @Version:1.0.0
* @Description:SWDC
*/
#pragma once

#include <iostream>

namespace SWDC {
	namespace kernel {
		static const char* kernelSource = R"CL(
unsigned char rgbToGrayscale(const unsigned char r,const unsigned char g,const unsigned char b) {
	return 0.299f * r + 0.587f * g + 0.114f * b;
}
void ima2cam(const int x,const int y, const int width, const int height, const float pixel, float *outX, float *outY)
{
    *outX = (x + 0.5 - width * 0.5) * pixel;
    *outY = -(y + 0.5 - height * 0.5) * pixel;
}
void cam2ima(const float x, const float y, const int width, const int height, const float pixel, float* outX, float* outY)
{
    *outX = (x / pixel) + width * 0.5 - 0.5;
    *outY = -(y / pixel) + height * 0.5 - 0.5;
}
void grd2cam(const float x, const float y , const float z, const float focal, const float* center, const float* rotation, float* outX, float* outY)
{
    float xdis = x - center[0];
    float ydis = y - center[1];
    float zdis = z - center[2];

    float fm = -focal / (rotation[2] * xdis + rotation[5] * ydis + rotation[8] * zdis);
    *outX = (rotation[0] * xdis + rotation[3] * ydis + rotation[6] * zdis) * fm;
    *outY = (rotation[1] * xdis + rotation[4] * ydis + rotation[7] * zdis) * fm;
}
void distoFunction(const float* params,const float x,const float y, float* outX, float* outY) {
    const float k1 = params[0], k2 = params[1], k3 = params[2];
    const float p1 = params[3], p2 = params[4];
    const float b1 = params[5], b2 = params[6];

    const float r2 = x * x + y * y;

    const float dx1 = x * (r2 * k1 + r2 * r2 * k2 + r2 * r2 * r2 * k3);
    const float dy1 = y * (r2 * k1 + r2 * r2 * k2 + r2 * r2 * r2 * k3);

    const float dx2 = (r2 + 2.0 * x * x) * p1 + 2.0 * x * y * p2;
    const float dy2 = (r2 + 2.0 * y * y) * p2 + 2.0 * x * y * p1;

    const float dx3 = x * b1 + y * b2;

    *outX = dx1 + dx2 + dx3;
    *outY = dy1 + dy2;
}
void removeDistortion(const float* params, const float x, const float y, float *outX, float *outY)
{
    const float epsilon = 0.0001; //criteria to stop the iteration
    int iter = 400;
    float x_u = x, y_u = y;
    float x_d = 0.0, y_d = 0.0;
    float x_p = 0.0, y_p = 0.0;

    while (iter > 0) //manhattan distance between the two points
    {
        distoFunction(params, x_u, y_u, &x_d, &y_d);
        x_u = x - x_d;
        y_u = y - y_d;
        if (fabs(x_d - x_p) > epsilon || fabs(y_d - y_p) > epsilon) {
            x_p = x_d;
            y_p = y_d;
            --iter;
        }
        else
        {
            break;
        }
    }

    *outX = x_u;
    *outY = y_u;
}
void bilinearInterpolation(float x, float y, int width, int height, int channels, const unsigned char* srcimage, unsigned char* out) {
	int u = (int)x;
	int v = (int)y;
	if (u >= 0 && u < width - 1 && v >= 0 && v < height - 1) {
		int LTIndex = (v * width + u) * channels;
		int LBIndex = ((v + 1) * width + u) * channels;
		int RTIndex = (v * width + (u + 1)) * channels;
		int RBIndex = ((v + 1) * width + (u + 1)) * channels;

		float dLx, dTy, dRx, dBy;
		dLx = x - u;
		dRx = u - x + 1;
		dTy = y - v;
		dBy = v - y + 1;

		out[0] = (dLx * srcimage[RTIndex] + dRx * srcimage[LTIndex]) * dBy + (dLx * srcimage[RBIndex] + dRx * srcimage[LBIndex]) * dTy;
		out[1] = (dLx * srcimage[RTIndex + 1] + dRx * srcimage[LTIndex + 1]) * dBy + (dLx * srcimage[RBIndex + 1] + dRx * srcimage[LBIndex + 1]) * dTy;
		out[2] = (dLx * srcimage[RTIndex + 2] + dRx * srcimage[LTIndex + 2]) * dBy + (dLx * srcimage[RBIndex + 2] + dRx * srcimage[LBIndex + 2]) * dTy;
	}
	else
	{
		out[0] = 0;
		out[1] = 0;
		out[2] = 0;
	}
}
float cubicKernelU(const unsigned char p0,const unsigned char p1,const unsigned char p2,const unsigned char p3, float x) {
    return p1 + ((p2 - p0) * 0.5) * x + (p0 + p2 * 2 - (p1 * 5 + p3) * 0.5) * x * x + ((p3 - p0 + (p1 - p2) * 3) * 0.5) * x * x * x;
}
float cubicKernelF(float p0, float p1, float p2, float p3, float x) {
    return p1 + ((p2 - p0) * 0.5) * x + (p0 + p2 * 2 - (p1 * 5 + p3) * 0.5) * x * x + ((p3 - p0 + (p1 - p2) * 3) * 0.5) * x * x * x;
}
void cubicInterpolation(float x, float y, int width, int height, int channels, const unsigned char* srcimage, unsigned char* out){
    int u = (int)x;
    int v = (int)y;
    if (u >= 1 && u < width - 2 && v >= 1 && v < height - 2) {
        int LTx = u - 1, LTy = v - 1;
        float ydis = y - v;
        float xdis = x - u;
        for (int k = 0; k < channels; ++k)
        {
            float r[4];
            for (int y = 0; y < 4; ++y)
            {
                r[y] = cubicKernelU(srcimage[((LTy + y) * width + LTx) * channels + k], srcimage[((LTy + y) * width + (LTx + 1)) * channels + k], srcimage[((LTy + y) * width + (LTx + 2)) * channels + k], srcimage[((LTy + y) * width + (LTx + 3)) * channels + k], xdis);
                if (r[y] < 0) r[y] = 0;
            }
            int temp = (int)(cubicKernelF(r[0], r[1], r[2], r[3], ydis) + 0.5);
            if (temp > 255) {
                out[k] = 255;
            }
            else if (temp < 0)
            {
                out[k] = 0;
            }
            else
            {
                out[k] = (unsigned char)temp;
            }
        }
    }else{
        out[0] = 0;
	    out[1] = 0;
	    out[2] = 0;
    }
}
__kernel void undistorted(const int width, const int height, const int channels, const float pixel, const float px, const float py, const int pp, const int interpolation, __global const float* params, __global const unsigned char* srcimage, __global unsigned char* outimage) {
    int y = get_global_id(0);
    int x = get_global_id(1);
    float xf = 0.0, yf = 0.0;
    ima2cam(x, y, width, height, pixel, &xf, &yf);
    removeDistortion(params, xf, yf, &xf, &yf);
    if(pp){
        xf += px;
        yf += py;
    }

    cam2ima(xf, yf, width, height, pixel, &xf, &yf);
    
    if(interpolation == 0){
 	    int xn = round(xf);
    	int yn = round(yf);
        if(xn >= 0 && xn < width && yn >= 0 && yn < height){
            int pixelOffset = (yn * width + xn) * channels;
            int index = (y * width + x) * channels;
            outimage[index] = srcimage[pixelOffset];
            outimage[index + 1] = srcimage[pixelOffset + 1];
            outimage[index + 2] = srcimage[pixelOffset + 2];
        }
    }else if(interpolation == 1){
        unsigned char out[3];
        bilinearInterpolation(xf, yf, width, height, channels, srcimage, out);
        int index = (y * width + x) * channels;
        outimage[index] = out[0];
        outimage[index + 1] = out[1];
        outimage[index + 2] = out[2];
    }else if(interpolation == 2){
        unsigned char out[3];
        cubicInterpolation(xf, yf, width, height, channels, srcimage, out);
        int index = (y * width + x) * channels;
        outimage[index] = out[0];
        outimage[index + 1] = out[1];
        outimage[index + 2] = out[2];
    }  
}
__kernel void correctionImage(const int width, const int height, const int channels, const float pixel, const float focal, const float xmin, const float ymax,const float z, const int groundWidth, const int groundHeight, const float groundSampleSize,const int interpolation, __global const float* center,__global const float* rotation,__global const unsigned char* srcimage,__global unsigned char* outimage) {
    int y = get_global_id(0);
    int x = get_global_id(1);
  
    float outx = 0.0, outy = 0.0;
    float yg = ymax - (y + 0.5) * groundSampleSize;
    float xg = xmin + (x + 0.5) * groundSampleSize;

    grd2cam(xg, yg, z, focal, center, rotation, &outx, &outy);
    cam2ima(outx, outy, width, height, pixel, &outx, &outy);

    if(interpolation == 0){
        int xn = round(outx);
        int yn = round(outy);

        if (xn >= 0 && xn < width && yn >= 0 && yn < height) {
            int pixelOffset = (yn * width + xn) * channels;
            int index = (y * groundWidth + x) * channels;

            outimage[index] = srcimage[pixelOffset];
            outimage[index + 1] = srcimage[pixelOffset + 1];
            outimage[index + 2] = srcimage[pixelOffset + 2];
        }
    }else if(interpolation == 1){
        unsigned char out[3];
        bilinearInterpolation(outx, outy, width, height, channels, srcimage, out);
        int index = (y * groundWidth + x) * channels;

        outimage[index] = out[0];
        outimage[index + 1] = out[1];
        outimage[index + 2] = out[2];
    }else if(interpolation == 2){
        unsigned char out[3];
        cubicInterpolation(outx, outy, width, height, channels, srcimage, out);
        int index = (y * groundWidth + x) * channels;

        outimage[index] = out[0];
        outimage[index + 1] = out[1];
        outimage[index + 2] = out[2];
    }
}
__kernel void correctionPartImage(const int width, const int height, const int channels, const float pixel, const float focal, const float xmin, const float ymax,const float z, const int groundWidth, const int groundHeight, const float groundSampleSize, const int interpolation,__global const float* center,__global const float* rotation,__global const unsigned char* srcimage,__global float* outimage) {
    int y = get_global_id(0);
    int x = get_global_id(1);

	int offsetY = get_global_offset(0);
	int offsetX = get_global_offset(1);
  
    float outx = 0.0, outy = 0.0;
    float yg = ymax - (y + 0.5) * groundSampleSize;
    float xg = xmin + (x + 0.5) * groundSampleSize;

    grd2cam(xg, yg, z, focal, center, rotation, &outx, &outy);
    cam2ima(outx, outy, width, height, pixel, &outx, &outy);

    if(interpolation == 0){
        int xn = round(outx);
        int yn = round(outy);

        if (xn >= 0 && xn < width && yn >= 0 && yn < height) {
            int pixelOffset = (yn * width + xn) * channels;
            int index = (y - offsetY) * groundWidth + (x - offsetX);
            outimage[index] = rgbToGrayscale(srcimage[pixelOffset], srcimage[pixelOffset + 1], srcimage[pixelOffset + 2]) / 255.0f;
        }
    }else if(interpolation == 1){
        unsigned char out[3];
        bilinearInterpolation(outx, outy, width, height, channels, srcimage, out);
        int index = (y - offsetY) * groundWidth + (x - offsetX);
        outimage[index] = rgbToGrayscale(out[0], out[1], out[2]) / 255.0f;
    }else if(interpolation == 2){
        unsigned char out[3];
        cubicInterpolation(outx, outy, width, height, channels, srcimage, out);
        int index = (y - offsetY) * groundWidth + (x - offsetX);
        outimage[index] = rgbToGrayscale(out[0], out[1], out[2]) / 255.0f;
    }
})CL";
	}
}
