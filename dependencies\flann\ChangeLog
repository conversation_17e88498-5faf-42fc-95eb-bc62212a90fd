Version 1.9.2
        * Removed redundant assignment (issue #422 @fluber)
        * Removed unnecessary null checks before delete (issue #420 @elfring)
        * Reverted PR #424 due to lack of portability
        * fscanf fix (PR #467 @rdelfin)
        * Out of bounds check (modified PR #455 @legrosbuffle, also included in PR #319 @seth-planet)
        * Fixed MacOS build (PR #470 @johnhe4, modified for linux)
	* Fixed build system with dummy.c hack (PR #457 @pemmanuelviel)
	* Fixed misleading indentation in util/any.h (PR #365 @psteinb, also PR #428, #430, #459)
	* Included datasets in repo
	* Correct typo in definition (PR #419 @SergioRAgostinho)
        * Fix typos (PR #279 @gadomski)
	* CMakefile CUDA sources fix (PR #458 @pemmanuelviel)
	* Documentation fix (PR #456 @pemmanuelviel)
	* Scoping issue fix (PR #405 @greenbrettmichael, also PR #469, issue #386)
	* Documentation fixes (PR #460 @pemmanuelviel)
	* Changed return value (PR #461 @pemmanuelviel)
	* Fixed CUDA crash - guarantee prealloc > 0 (PR #437 @neka-rat)
	* Fixed wrong variable use (PR #433 @XinyiYS)
	* Fixed RNG initialization (PR #424 @SiddhantRanade)
	* Updated link to PDF (PR #474 @kleinma)

Version 1.6.11
	* bug fixes

Version 1.6.10
	* fixed a radiusSearch bug introduced in 1.6.9

Version 1.6.9
	* bug fixes
	* fixed radius search bug on MSVC compiler
	* fixed windows linking problems

Version 1.6.8
	* bug fixes, low dimensional search speedup

Version 1.6.7
	* bug fixes

Version 1.6.6
	* misc bug fixes

Version 1.6.5
	* fix compilation problem on some C++ compilers
	* fixes in the python bindings

Version 1.6.4
	* small bug fix

Version 1.6.3
	* radius search speedup

Version 1.6.2
	* slight API changes to the C++ bindings, now the main index type is templated
	* on the distance functor which makes it easier to use custom distances
	* new kd-tree implementation optimized for low dimensionality data
	* experimental MPI support for cluster computing

Version 1.5
	* new C++ templated API
	* saving/loading of indices to disk
	* threadsafe search
	* new distance types (thanks to Radu Bogdan Rusu and Romain Thibaux for the patch)
	* (api change) autotuned is no longer selected by passing a precision >0, it's used when the algorithm type is set to autotuned
