#include "ImageColor.hpp"
#include <algorithm>
#include <opencv2/opencv.hpp>

namespace SWDC {
	namespace stitch {
		EImageColorCorrection EImageColorCorrection_stringToEnum(const std::string& colorType)
		{
			if (colorType == "Original_Color")           return EImageColorCorrection::ORIGINALCOLOR;
			if (colorType == "Gray_World")                   return EImageColorCorrection::GRAYWORLD;
			if (colorType == "Histogram_Matching")       return EImageColorCorrection::HISTOGRAMMATCHING;
			throw std::out_of_range("Invalid colorType: " + colorType);
		}

		void imageHistogramMatching(const image::Image<Vec3c>& targetImage, image::Image<Vec3c>* sourceImage)
		{
			const unsigned char* targetImageDataPtr = reinterpret_cast<const unsigned char*>(targetImage.data());
			const unsigned char* sourceImageDataPtr = reinterpret_cast<const unsigned char*>(sourceImage->data());

			cv::Mat targetImageMat(targetImage.rows(), targetImage.cols(), CV_MAKETYPE(CV_8U, 3), const_cast<unsigned char*>(targetImageDataPtr));
			cv::Mat sourceImageMat(sourceImage->rows(), sourceImage->cols(), CV_MAKETYPE(CV_8U, 3), const_cast<unsigned char*>(sourceImageDataPtr));

			std::vector<cv::Mat> sourceChannels, targetChannels;
			cv::split(sourceImageMat, sourceChannels);
			cv::split(targetImageMat, targetChannels);

			std::vector<cv::Mat> resultChannels(3);

			for (int i = 0; i < 3; ++i) {
				cv::Mat sourceHist, targetHist;
				int histSize = 256;
				float range[] = { 0, 256 };
				const float* histRange = { range };

				cv::calcHist(&sourceChannels[i], 1, nullptr, cv::Mat(), sourceHist, 1, &histSize, &histRange);
				cv::calcHist(&targetChannels[i], 1, nullptr, cv::Mat(), targetHist, 1, &histSize, &histRange);

				cv::Mat sourceCumHist = sourceHist.clone();
				cv::Mat targetCumHist = targetHist.clone();
				for (int j = 1; j < histSize; ++j) {
					sourceCumHist.at<float>(j) += sourceCumHist.at<float>(j - 1);
					targetCumHist.at<float>(j) += targetCumHist.at<float>(j - 1);
				}

				cv::Mat mappingFunction(histSize, 1, CV_8U);
				for (int j = 0; j < histSize; ++j) {
					float probSource = sourceCumHist.at<float>(j) / sourceCumHist.at<float>(histSize - 1);
					for (int k = 0; k < histSize; ++k) {
						float probTarget = targetCumHist.at<float>(k) / targetCumHist.at<float>(histSize - 1);
						if (probTarget >= probSource) {
							mappingFunction.at<uchar>(j) = k;
							break;
						}
					}
				}

				cv::LUT(sourceChannels[i], mappingFunction, resultChannels[i]);
			}


			cv::Mat resultImageMat;
			cv::merge(resultChannels, resultImageMat);
			std::memcpy(sourceImage->data(), resultImageMat.data, resultImageMat.total() * resultImageMat.elemSize());
		}

		void grayWorldAlgorithm(image::Image<Vec3c>* image1, image::Image<Vec3c>* image2) {

			const unsigned char* image1DataPtr = reinterpret_cast<const unsigned char*>(image1->data());
			const unsigned char* image2DataPtr = reinterpret_cast<const unsigned char*>(image2->data());

			cv::Mat image1Mat(image1->rows(), image1->cols(), CV_MAKETYPE(CV_8U, 3), const_cast<unsigned char*>(image1DataPtr));
			cv::Mat image2Mat(image2->rows(), image2->cols(), CV_MAKETYPE(CV_8U, 3), const_cast<unsigned char*>(image2DataPtr));

			cv::cvtColor(image1Mat, image1Mat, cv::COLOR_RGB2BGR);
			cv::cvtColor(image2Mat, image2Mat, cv::COLOR_RGB2BGR);

			std::vector<cv::Mat> bgr1, bgr2;
			cv::split(image1Mat, bgr1);
			cv::split(image2Mat, bgr2);

			double mean_b1 = cv::mean(bgr1[0])[0];
			double mean_g1 = cv::mean(bgr1[1])[0];
			double mean_r1 = cv::mean(bgr1[2])[0];
			double mean_b2 = cv::mean(bgr2[0])[0];
			double mean_g2 = cv::mean(bgr2[1])[0];
			double mean_r2 = cv::mean(bgr2[2])[0];

			double kb = (mean_g1 + mean_g2) / (mean_b1 + mean_b2);
			double kr = (mean_g1 + mean_g2) / (mean_r1 + mean_r2);

			cv::Mat b_adjusted1 = bgr1[0] * kb;
			cv::Mat r_adjusted1 = bgr1[2] * kr;
			cv::Mat b_adjusted2 = bgr2[0] * kb;
			cv::Mat r_adjusted2 = bgr2[2] * kr;

			std::vector<cv::Mat> channels_adjusted1 = { b_adjusted1, bgr1[1], r_adjusted1 };
			std::vector<cv::Mat> channels_adjusted2 = { b_adjusted2, bgr2[1], r_adjusted2 };
			cv::Mat img_adjusted1, img_adjusted2;
			cv::merge(channels_adjusted1, img_adjusted1);
			cv::merge(channels_adjusted2, img_adjusted2);

			cv::cvtColor(img_adjusted1, img_adjusted1, cv::COLOR_BGR2RGB);
			cv::cvtColor(img_adjusted2, img_adjusted2, cv::COLOR_BGR2RGB);

			std::memcpy(image1->data(), img_adjusted1.data, img_adjusted1.total() * img_adjusted1.elemSize());
			std::memcpy(image2->data(), img_adjusted2.data, img_adjusted2.total() * img_adjusted2.elemSize());
		}

		void grayWorldAlgorithm(image::Image<Vec3c>* image1, image::Image<Vec3c>* image2, image::Image<Vec3c>* image3) {

			const unsigned char* image1DataPtr = reinterpret_cast<const unsigned char*>(image1->data());
			const unsigned char* image2DataPtr = reinterpret_cast<const unsigned char*>(image2->data());
			const unsigned char* image3DataPtr = reinterpret_cast<const unsigned char*>(image3->data());

			cv::Mat image1Mat(image1->rows(), image1->cols(), CV_MAKETYPE(CV_8U, 3), const_cast<unsigned char*>(image1DataPtr));
			cv::Mat image2Mat(image2->rows(), image2->cols(), CV_MAKETYPE(CV_8U, 3), const_cast<unsigned char*>(image2DataPtr));
			cv::Mat image3Mat(image3->rows(), image3->cols(), CV_MAKETYPE(CV_8U, 3), const_cast<unsigned char*>(image3DataPtr));

			cv::cvtColor(image1Mat, image1Mat, cv::COLOR_RGB2BGR);
			cv::cvtColor(image2Mat, image2Mat, cv::COLOR_RGB2BGR);
			cv::cvtColor(image3Mat, image3Mat, cv::COLOR_RGB2BGR);

			std::vector<cv::Mat> bgr1, bgr2, bgr3;
			cv::split(image1Mat, bgr1);
			cv::split(image2Mat, bgr2);
			cv::split(image3Mat, bgr3);

			double mean_b1 = cv::mean(bgr1[0])[0];
			double mean_g1 = cv::mean(bgr1[1])[0];
			double mean_r1 = cv::mean(bgr1[2])[0];
			double mean_b2 = cv::mean(bgr2[0])[0];
			double mean_g2 = cv::mean(bgr2[1])[0];
			double mean_r2 = cv::mean(bgr2[2])[0];
			double mean_b3 = cv::mean(bgr3[0])[0];
			double mean_g3 = cv::mean(bgr3[1])[0];
			double mean_r3 = cv::mean(bgr3[2])[0];

			double kb = (mean_g1 + mean_g2 + mean_g3) / (mean_b1 + mean_b2 + mean_b3);
			double kr = (mean_g1 + mean_g2 + mean_g3) / (mean_r1 + mean_r2 + mean_r3);

			cv::Mat b_adjusted1 = bgr1[0] * kb;
			cv::Mat r_adjusted1 = bgr1[2] * kr;
			cv::Mat b_adjusted2 = bgr2[0] * kb;
			cv::Mat r_adjusted2 = bgr2[2] * kr;
			cv::Mat b_adjusted3 = bgr3[0] * kb;
			cv::Mat r_adjusted3 = bgr3[2] * kr;

			std::vector<cv::Mat> channels_adjusted1 = { b_adjusted1, bgr1[1], r_adjusted1 };
			std::vector<cv::Mat> channels_adjusted2 = { b_adjusted2, bgr2[1], r_adjusted2 };
			std::vector<cv::Mat> channels_adjusted3 = { b_adjusted3, bgr3[1], r_adjusted3 };
			cv::Mat img_adjusted1, img_adjusted2, img_adjusted3;
			cv::merge(channels_adjusted1, img_adjusted1);
			cv::merge(channels_adjusted2, img_adjusted2);
			cv::merge(channels_adjusted3, img_adjusted3);

			cv::cvtColor(img_adjusted1, img_adjusted1, cv::COLOR_BGR2RGB);
			cv::cvtColor(img_adjusted2, img_adjusted2, cv::COLOR_BGR2RGB);
			cv::cvtColor(img_adjusted3, img_adjusted3, cv::COLOR_BGR2RGB);

			std::memcpy(image1->data(), img_adjusted1.data, img_adjusted1.total() * img_adjusted1.elemSize());
			std::memcpy(image2->data(), img_adjusted2.data, img_adjusted2.total() * img_adjusted2.elemSize());
			std::memcpy(image3->data(), img_adjusted3.data, img_adjusted3.total() * img_adjusted3.elemSize());
		}

		void grayWorldAlgorithm(image::Image<Vec3c>* image1, image::Image<Vec3c>* image2, image::Image<Vec3c>* image3, image::Image<Vec3c>* image4) {

			const unsigned char* image1DataPtr = reinterpret_cast<const unsigned char*>(image1->data());
			const unsigned char* image2DataPtr = reinterpret_cast<const unsigned char*>(image2->data());
			const unsigned char* image3DataPtr = reinterpret_cast<const unsigned char*>(image3->data());
			const unsigned char* image4DataPtr = reinterpret_cast<const unsigned char*>(image4->data());

			cv::Mat image1Mat(image1->rows(), image1->cols(), CV_MAKETYPE(CV_8U, 3), const_cast<unsigned char*>(image1DataPtr));
			cv::Mat image2Mat(image2->rows(), image2->cols(), CV_MAKETYPE(CV_8U, 3), const_cast<unsigned char*>(image2DataPtr));
			cv::Mat image3Mat(image3->rows(), image3->cols(), CV_MAKETYPE(CV_8U, 3), const_cast<unsigned char*>(image3DataPtr));
			cv::Mat image4Mat(image4->rows(), image4->cols(), CV_MAKETYPE(CV_8U, 3), const_cast<unsigned char*>(image4DataPtr));

			cv::cvtColor(image1Mat, image1Mat, cv::COLOR_RGB2BGR);
			cv::cvtColor(image2Mat, image2Mat, cv::COLOR_RGB2BGR);
			cv::cvtColor(image3Mat, image3Mat, cv::COLOR_RGB2BGR);
			cv::cvtColor(image4Mat, image4Mat, cv::COLOR_RGB2BGR);

			std::vector<cv::Mat> bgr1, bgr2, bgr3, bgr4;
			cv::split(image1Mat, bgr1);
			cv::split(image2Mat, bgr2);
			cv::split(image3Mat, bgr3);
			cv::split(image4Mat, bgr4);

			double mean_b1 = cv::mean(bgr1[0])[0];
			double mean_g1 = cv::mean(bgr1[1])[0];
			double mean_r1 = cv::mean(bgr1[2])[0];
			double mean_b2 = cv::mean(bgr2[0])[0];
			double mean_g2 = cv::mean(bgr2[1])[0];
			double mean_r2 = cv::mean(bgr2[2])[0];
			double mean_b3 = cv::mean(bgr3[0])[0];
			double mean_g3 = cv::mean(bgr3[1])[0];
			double mean_r3 = cv::mean(bgr3[2])[0];
			double mean_b4 = cv::mean(bgr4[0])[0];
			double mean_g4 = cv::mean(bgr4[1])[0];
			double mean_r4 = cv::mean(bgr4[2])[0];

			double kb = (mean_g1 + mean_g2 + mean_g3 + mean_g4) / (mean_b1 + mean_b2 + mean_b3 + mean_b4);
			double kr = (mean_g1 + mean_g2 + mean_g3 + mean_g4) / (mean_r1 + mean_r2 + mean_r3 + mean_r4);

			cv::Mat b_adjusted1 = bgr1[0] * kb;
			cv::Mat r_adjusted1 = bgr1[2] * kr;
			cv::Mat b_adjusted2 = bgr2[0] * kb;
			cv::Mat r_adjusted2 = bgr2[2] * kr;
			cv::Mat b_adjusted3 = bgr3[0] * kb;
			cv::Mat r_adjusted3 = bgr3[2] * kr;
			cv::Mat b_adjusted4 = bgr4[0] * kb;
			cv::Mat r_adjusted4 = bgr4[2] * kr;

			std::vector<cv::Mat> channels_adjusted1 = { b_adjusted1, bgr1[1], r_adjusted1 };
			std::vector<cv::Mat> channels_adjusted2 = { b_adjusted2, bgr2[1], r_adjusted2 };
			std::vector<cv::Mat> channels_adjusted3 = { b_adjusted3, bgr3[1], r_adjusted3 };
			std::vector<cv::Mat> channels_adjusted4 = { b_adjusted4, bgr4[1], r_adjusted4 };
			cv::Mat img_adjusted1, img_adjusted2, img_adjusted3, img_adjusted4;
			cv::merge(channels_adjusted1, img_adjusted1);
			cv::merge(channels_adjusted2, img_adjusted2);
			cv::merge(channels_adjusted3, img_adjusted3);
			cv::merge(channels_adjusted4, img_adjusted4);

			cv::cvtColor(img_adjusted1, img_adjusted1, cv::COLOR_BGR2RGB);
			cv::cvtColor(img_adjusted2, img_adjusted2, cv::COLOR_BGR2RGB);
			cv::cvtColor(img_adjusted3, img_adjusted3, cv::COLOR_BGR2RGB);
			cv::cvtColor(img_adjusted4, img_adjusted4, cv::COLOR_BGR2RGB);

			std::memcpy(image1->data(), img_adjusted1.data, img_adjusted1.total() * img_adjusted1.elemSize());
			std::memcpy(image2->data(), img_adjusted2.data, img_adjusted2.total() * img_adjusted2.elemSize());
			std::memcpy(image3->data(), img_adjusted3.data, img_adjusted3.total() * img_adjusted3.elemSize());
			std::memcpy(image4->data(), img_adjusted4.data, img_adjusted4.total() * img_adjusted4.elemSize());
		}
	}
}
