% VL_IHASHFIND  Find labels in an integer hash table
%   SEL = VL_IHASHFIND(ID, NEXT, K, X) returns a vector SEL of the
%   entires in the hash table ID,NEXT,K corresponding to the labels
%   stored as columns of X.
%
%   The format is the same as for the functino VL_IHASHSUM().
%
%   See also: VL_IHASHSUM().

% Author: <PERSON>

% Copyright (C) 2008-12 <PERSON>.
% All rights reserved.
%
% This file is part of the VLFeat library and is made available under
% the terms of the BSD license (see the COPYING file).
