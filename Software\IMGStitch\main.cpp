#include <QGuiApplication>
#include <QQmlApplicationEngine>
#include <QTextCodec>
#include <QQmlContext>
#include <QIcon>
#include "imgStitch.h"

int main(int argc, char* argv[])
{
#if defined(Q_OS_WIN)
	QCoreApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
#endif

	QGuiApplication app(argc, argv);
	app.setOrganizationName("IMGStitch");
	app.setWindowIcon(QIcon(":/image/puzzle.ico"));

	QTextCodec* codec = QTextCodec::codecForName("GBK");
	QTextCodec::setCodecForLocale(codec);
	
	QQmlApplicationEngine engine;
	imgStitch imgStitch;
	auto rootContext = engine.rootContext();
	rootContext->setContextProperty("imgStitch", &imgStitch);
	
	engine.load(QUrl(QStringLiteral("qrc:/qt/qml/imgstitch/main.qml")));
	if (engine.rootObjects().isEmpty())
		return -1;

	return app.exec();
}
