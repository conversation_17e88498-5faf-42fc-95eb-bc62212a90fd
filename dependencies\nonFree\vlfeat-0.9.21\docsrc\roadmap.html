<!DOCTYPE group PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<group>

<p><img src="images/PASCAL2.png" style="height:6em;float:left;"
alt="PASCAL2 credits"></img> In 2012 the development of VLFeat is
supported by the PASCAL Harvest programme. Several people have been
working in Oxford to add new functionalities to the library. Moreover,
leading researchers in computer vision were consulted as
<a href="%pathto:about.advisors">advisors</a> of the project. These
contributions will be made public in the next several weeks as the
code is tuned and finalised.</p>

<p>A particularly significant contribution is the creation of a new
sub-project, <b>VLBenchmakrs</b>, for the evaluation of feature
detectors and descriptors. <b>VLBenchmarks</b> is meant to provide a
future-proof benchmarking suite. The first release includes
reimplementations of standard feature benchmarks to replace ageing
legacy code and a brand new benchmark. In the future, it will be used
to deliver to the community new, modern benchmarks in a consistent and
simple to use pacakge.</p>

<h4>Version 0.9.17 (released)</h4>
<ul>
<li>Large scale Approximate Nearest Neighbours (ANN) K-means.</li>
<li>Gaussian Mixture Models (GMMs).</li>
<li>Fisher Kernel encoding.</li>
<li>VLAD encoding.</li>
<li>New SVM algorithms: SGD and SDCA with support for multiple loss
functions, data weighting, and other useful features.</li>
<li>LIOP feature descriptor.</li>
<li>Multicore computations through OpenMP.</li>
<li>New extensive image recognition examples running on several
standard computer vision benchmark datasets.</li>
<li>Improved documentation.</li>
</ul>

<h4>Version 0.9.16 (released)</h4>
<ul>
<li><b>VLBenchmarks</b>: detector repeatability, descriptor matching
score, and image retrieval benchmarks.</li>
<li>Affine covariant feature detectors. The aim is to create a future
proof replacement of
the <a href='http://www.robots.ox.ac.uk/~vgg/research/affine/'>legacy
implementation</a>.</li>
</ul>

<h4>Version 0.9.15 (released)</h4>
<ul>
<li>Histogram of Oriented Gradients (HOG) features.</li>
<li>New Stochastic Gradient Descent (SGD) Support Vector Machine
(SVM) implementation. Layed down a good infrastructure to add more
learning algorithms in the future.</li>
<li>Several utility functions (hashed summation, integral images,
plotting functions).</li>
<li>Significant cleanup of the codebase, outstanding bugs
squashed.</li>
</ul>
</group>
