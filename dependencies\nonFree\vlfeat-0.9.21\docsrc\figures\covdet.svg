<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="595.276" height="841.89" viewBox="0, 0, 595.276, 841.89">
  <g id="Layer 1">
    <g>
      <path d="M85.539,85.539 L255.618,85.539 L255.618,213.098 L85.539,213.098 z" fill="#FFFFFF"/>
      <path d="M85.539,85.539 L255.618,85.539 L255.618,213.098 L85.539,213.098 z" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    </g>
    <g>
      <path d="M354.831,85.539 L524.909,85.539 L524.909,213.098 L354.831,213.098 z" fill="#FFFFFF"/>
      <path d="M354.831,85.539 L524.909,85.539 L524.909,213.098 L354.831,213.098 z" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    </g>
    <g>
      <path d="M148.482,186.031 L176.607,186.031 C176.607,182.033 176.322,181.24 169.7,181.24 C170.758,178.066 175.058,170.389 177.447,170.389 C179.561,170.389 182.083,170.505 182.083,175.328 C182.083,180.804 191.298,189.633 193.532,186.031 C196.862,180.663 187.808,182.3 187.808,169.355 C187.808,151.645 198.143,154.166 198.143,144.216 C198.143,139.238 196.768,138.566 196.768,135.007 C196.768,130.303 200.912,130.576 200.105,126.676 C199.548,123.983 199.113,121.822 198.788,118.565 C198.558,116.253 198.484,113.79 196.083,113.889 C193.262,114.005 192.192,118.97 187.808,119.326 C183.432,119.682 179.701,115.601 178.007,116.184 C176.328,116.762 176.856,121.318 177.852,124.553 C179.416,129.639 182.83,136.251 176.607,137.247 C170.385,138.243 159.682,139.238 151.966,149.443 C144.251,159.648 144.535,171.624 141.513,175.328 C131.368,187.761 120.972,182.656 120.972,193 C120.972,197.631 128.819,200.965 130.063,199.471 C131.308,197.978 119.489,193.528 132.713,188.271 C144.011,183.78 145.02,182.863 148.482,186.031 z" fill="#D6E4FF"/>
      <path d="M148.482,186.031 L176.607,186.031 C176.607,182.033 176.322,181.24 169.7,181.24 C170.758,178.066 175.058,170.389 177.447,170.389 C179.561,170.389 182.083,170.505 182.083,175.328 C182.083,180.804 191.298,189.633 193.532,186.031 C196.862,180.663 187.808,182.3 187.808,169.355 C187.808,151.645 198.143,154.166 198.143,144.216 C198.143,139.238 196.768,138.566 196.768,135.007 C196.768,130.303 200.912,130.576 200.105,126.676 C199.548,123.983 199.113,121.822 198.788,118.565 C198.558,116.253 198.484,113.79 196.083,113.889 C193.262,114.005 192.192,118.97 187.808,119.326 C183.432,119.682 179.701,115.601 178.007,116.184 C176.328,116.762 176.856,121.318 177.852,124.553 C179.416,129.639 182.83,136.251 176.607,137.247 C170.385,138.243 159.682,139.238 151.966,149.443 C144.251,159.648 144.535,171.624 141.513,175.328 C131.368,187.761 120.972,182.656 120.972,193 C120.972,197.631 128.819,200.965 130.063,199.471 C131.308,197.978 119.489,193.528 132.713,188.271 C144.011,183.78 145.02,182.863 148.482,186.031 z" fill-opacity="0" stroke="#96A2BB" stroke-width="2"/>
    </g>
    <g>
      <path d="M450.472,182.435 L460.146,171.567 C456.206,168.06 455.326,167.475 453.049,170.034 C450.286,166.842 444.199,158.447 445.021,157.523 C445.748,156.706 446.73,155.834 451.483,160.064 C456.879,164.867 468.748,169.049 465.967,165.027 C461.823,159.032 460.322,163.967 447.566,152.613 C430.115,137.081 436.154,135.298 426.35,126.571 C421.444,122.205 420.309,122.147 416.802,119.026 C412.167,114.901 413.861,113.539 409.741,110.43 C406.895,108.282 404.617,106.556 401.295,103.825 C398.938,101.886 396.485,99.754 395.757,100.769 C394.902,101.961 399.426,106.729 398.269,108.736 C397.114,110.738 391.81,108.601 391.802,109.767 C391.794,110.923 396.464,114.714 399.995,117.167 C405.545,121.023 413.234,125.503 412.075,128.781 C410.916,132.059 408.216,137.068 415.618,148.999 C423.02,160.931 434.919,171.325 437.529,175.742 C446.291,190.566 437.685,190.106 447.878,199.178 C452.441,203.239 458.425,203.131 457.381,201.34 C456.338,199.55 447.888,200.214 447.256,190.493 C446.716,182.188 446.16,180.995 450.472,182.435 z" fill="#D6E4FF"/>
      <path d="M450.472,182.435 L460.146,171.567 C456.206,168.06 455.326,167.475 453.049,170.034 C450.286,166.842 444.199,158.447 445.021,157.523 C445.748,156.706 446.73,155.834 451.483,160.064 C456.879,164.867 468.748,169.049 465.967,165.027 C461.823,159.032 460.322,163.967 447.566,152.613 C430.115,137.081 436.154,135.298 426.35,126.571 C421.444,122.205 420.309,122.147 416.802,119.026 C412.167,114.901 413.861,113.539 409.741,110.43 C406.895,108.282 404.617,106.556 401.295,103.825 C398.938,101.886 396.485,99.754 395.757,100.769 C394.902,101.961 399.426,106.729 398.269,108.736 C397.114,110.738 391.81,108.601 391.802,109.767 C391.794,110.923 396.464,114.714 399.995,117.167 C405.545,121.023 413.234,125.503 412.075,128.781 C410.916,132.059 408.216,137.068 415.618,148.999 C423.02,160.931 434.919,171.325 437.529,175.742 C446.291,190.566 437.685,190.106 447.878,199.178 C452.441,203.239 458.425,203.131 457.381,201.34 C456.338,199.55 447.888,200.214 447.256,190.493 C446.716,182.188 446.16,180.995 450.472,182.435 z" fill-opacity="0" stroke="#96A2BB" stroke-width="2"/>
    </g>
    <g>
      <path d="M85.539,283.965 L255.618,283.965 L255.618,411.524 L85.539,411.524 z" fill="#FFFFFF"/>
      <path d="M85.539,283.965 L255.618,283.965 L255.618,411.524 L85.539,411.524 z" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    </g>
    <g>
      <path d="M354.831,283.965 L524.909,283.965 L524.909,411.524 L354.831,411.524 z" fill="#FFFFFF"/>
      <path d="M354.831,283.965 L524.909,283.965 L524.909,411.524 L354.831,411.524 z" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    </g>
    <path d="M147.982,383.956 L176.107,383.956 C176.107,379.958 175.822,379.165 169.2,379.165 C170.258,375.991 174.558,368.314 176.947,368.314 C179.061,368.314 181.583,368.43 181.583,373.254 C181.583,378.729 190.798,387.558 193.032,383.956 C196.362,378.588 187.308,380.225 187.308,367.28 C187.308,349.57 197.643,352.091 197.643,342.141 C197.643,337.163 196.268,336.491 196.268,332.932 C196.268,328.229 200.412,328.502 199.605,324.602 C199.048,321.908 198.613,319.747 198.288,316.49 C198.058,314.178 197.984,311.715 195.583,311.814 C192.762,311.93 191.692,316.896 187.308,317.252 C182.932,317.607 179.201,313.527 177.507,314.109 C175.828,314.687 176.356,319.243 177.352,322.478 C178.916,327.564 182.33,334.177 176.107,335.172 C169.885,336.168 159.182,337.163 151.466,347.368 C143.751,357.573 144.035,369.549 141.013,373.254 C130.868,385.686 120.472,380.581 120.472,390.925 C120.472,395.556 128.319,398.89 129.563,397.397 C130.808,395.903 118.989,391.453 132.213,386.196 C143.511,381.705 144.52,380.788 147.982,383.956 z" fill="#D7D4D8"/>
    <g>
      <path d="M187.795,340.158 C178.011,340.158 170.079,332.226 170.079,322.441 C170.079,312.656 178.011,304.724 187.795,304.724 C197.58,304.724 205.512,312.656 205.512,322.441 C205.512,332.226 197.58,340.158 187.795,340.158 z" fill-opacity="0" stroke="#69F903" stroke-width="4"/>
      <path d="M187.795,340.158 C178.011,340.158 170.079,332.226 170.079,322.441 C170.079,312.656 178.011,304.724 187.795,304.724 C197.58,304.724 205.512,312.656 205.512,322.441 C205.512,332.226 197.58,340.158 187.795,340.158 z" fill-opacity="0" stroke="#000000" stroke-width="0.5"/>
    </g>
    <path d="M449.972,380.36 L459.646,369.492 C455.706,365.986 454.826,365.4 452.549,367.959 C449.786,364.767 443.699,356.372 444.521,355.448 C445.248,354.632 446.23,353.759 450.983,357.99 C456.379,362.792 468.248,366.974 465.467,362.952 C461.323,356.958 459.822,361.892 447.066,350.538 C429.615,335.006 435.654,333.223 425.85,324.497 C420.944,320.131 419.809,320.073 416.302,316.951 C411.667,312.826 413.361,311.464 409.241,308.355 C406.395,306.208 404.117,304.481 400.795,301.75 C398.438,299.812 395.985,297.679 395.257,298.694 C394.402,299.886 398.926,304.654 397.769,306.661 C396.614,308.663 391.31,306.526 391.302,307.692 C391.294,308.848 395.964,312.639 399.495,315.092 C405.045,318.948 412.734,323.429 411.575,326.706 C410.416,329.984 407.716,334.993 415.118,346.925 C422.52,358.856 434.419,369.25 437.029,373.667 C445.791,388.491 437.185,388.031 447.378,397.103 C451.941,401.165 457.925,401.056 456.881,399.266 C455.838,397.475 447.388,398.139 446.756,388.418 C446.216,380.114 445.66,378.92 449.972,380.36 z" fill="#D7D4D8"/>
    <g>
      <path d="M420.508,326.562 C417.142,330.343 406.598,326.451 396.957,317.87 C387.315,309.288 382.227,299.266 385.593,295.486 C388.958,291.704 399.502,295.596 409.144,304.178 C418.785,312.759 423.873,322.781 420.508,326.562 z" fill-opacity="0" stroke="#69F903" stroke-width="4"/>
      <path d="M420.508,326.562 C417.142,330.343 406.598,326.451 396.957,317.87 C387.315,309.288 382.227,299.266 385.593,295.486 C388.958,291.704 399.502,295.596 409.144,304.178 C418.785,312.759 423.873,322.781 420.508,326.562 z" fill-opacity="0" stroke="#000000" stroke-width="0.5"/>
    </g>
    <g>
      <path d="M170.579,227.272 L170.579,260.791" fill-opacity="0" stroke="#000000" stroke-width="1"/>
      <path d="M167.579,260.791 L170.579,268.791 L173.579,260.791 z" fill="#000000" fill-opacity="1" stroke="#000000" stroke-width="1" stroke-opacity="1"/>
    </g>
    <g>
      <path d="M439.87,227.272 L439.87,260.791" fill-opacity="0" stroke="#000000" stroke-width="1"/>
      <path d="M436.87,260.791 L439.87,268.791 L442.87,260.791 z" fill="#000000" fill-opacity="1" stroke="#000000" stroke-width="1" stroke-opacity="1"/>
    </g>
    <text transform="matrix(1, 0, 0, 1, 196.669, 246.563)">
      <tspan x="-16.26" y="3" font-family="Helvetica" font-size="9" fill="#000000">detector</tspan>
    </text>
    <text transform="matrix(1, 0, 0, 1, 463.464, 246.563)">
      <tspan x="-16.26" y="3" font-family="Helvetica" font-size="9" fill="#000000">detector</tspan>
    </text>
    <g>
      <path d="M269.791,149.319 L338.744,149.319" fill-opacity="0" stroke="#000000" stroke-width="1"/>
      <path d="M338.744,152.319 L346.744,149.319 L338.744,146.319 z" fill="#000000" fill-opacity="1" stroke="#000000" stroke-width="1" stroke-opacity="1"/>
    </g>
    <text transform="matrix(1, 0, 0, 1, 308.268, 161.906)">
      <tspan x="-9.754" y="3" font-family="Helvetica" font-size="9" fill="#000000">warp</tspan>
    </text>
    <g>
      <path d="M269.791,354.831 L338.744,354.831" fill-opacity="0" stroke="#000000" stroke-width="1"/>
      <path d="M338.744,357.831 L346.744,354.831 L338.744,351.831 z" fill="#000000" fill-opacity="1" stroke="#000000" stroke-width="1" stroke-opacity="1"/>
    </g>
    <text transform="matrix(1, 0, 0, 1, 308.268, 367.417)">
      <tspan x="-9.754" y="3" font-family="Helvetica" font-size="9" fill="#000000">warp</tspan>
    </text>
  </g>
  <defs/>
</svg>
