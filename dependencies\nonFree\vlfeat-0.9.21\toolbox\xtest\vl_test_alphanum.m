classdef vl_test_alphanum < matlab.unittest.TestCase
  properties
    strings = ...
      {'1000X Radonius Maximus','10X Radonius','200X Radonius','20X Radonius', ...
      '20X Radonius Prime','30X Radonius','40X Radonius','Allegia 50 Clasteron', ...
      'Allegia 500 Clasteron','Allegia 50B Clasteron','Allegia 51 Clasteron', ...
      'Allegia 6R Clasteron','Alpha 100','Alpha 2','Alpha 200','Alpha 2A', ...
      'Alpha 2A-8000','Alpha 2A-900','Callisto Morphamax','Callisto Morphamax 500', ...
      'Callisto Morphamax 5000','Callisto Morphamax 600','Callisto Morphamax 6000 SE', ...
      'Callisto Morphamax 6000 SE2','Callisto Morphamax 700','Callisto Morphamax 7000', ...
      '<PERSON><PERSON> Xlater 10000','<PERSON><PERSON>later 2000','<PERSON>ph Xlater 300','<PERSON><PERSON>r 40','<PERSON>ph Xlater 5', ...
      '<PERSON><PERSON>later 50','<PERSON><PERSON> Xlater 500','<PERSON><PERSON> Xlater 5000','<PERSON><PERSON> Xlater 58'} ;
    sortedStrings = ...
      {'10X <PERSON>donius','20X Radonius','20X Radonius Prime','30X Radonius',...
      '40X Radonius','200X Radonius','1000X Radonius Maximus','Allegia 6R Clasteron',...
      'Allegia 50 Clasteron','Allegia 50B Clasteron','Allegia 51 Clasteron',...
      'Allegia 500 Clasteron','Alpha 2','Alpha 2A','Alpha 2A-900','Alpha 2A-8000',...
      'Alpha 100','Alpha 200','Callisto Morphamax','Callisto Morphamax 500',...
      'Callisto Morphamax 600','Callisto Morphamax 700','Callisto Morphamax 5000',...
      'Callisto Morphamax 6000 SE','Callisto Morphamax 6000 SE2','Callisto Morphamax 7000',...
      'Xiph Xlater 5','Xiph Xlater 40','Xiph Xlater 50','Xiph Xlater 58','Xiph Xlater 300',...
      'Xiph Xlater 500','Xiph Xlater 2000','Xiph Xlater 5000','Xiph Xlater 10000'} ;
  end
  
  methods (Test)
    function test_basic(t)
      sorted = vl_alphanum(t.strings) ;
      t.verifyEqual(sorted,t.sortedStrings) ;
    end
  end
end
