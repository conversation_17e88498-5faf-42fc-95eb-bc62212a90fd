// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		2D0E8A7C1786D18D005419DC /* libvl.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 2D1EECD21603E85200C63DCE /* libvl.dylib */; };
		2D117999178C1EA900311182 /* mathop_avx.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D117997178C1EA900311182 /* mathop_avx.c */; };
		2D11799A178C1EA900311182 /* mathop_avx.h in Headers */ = {isa = PBXBuildFile; fileRef = 2D117998178C1EA900311182 /* mathop_avx.h */; };
		2D1BC0F6160DF170009E8DD3 /* covdet.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D1EED211604FE5900C63DCE /* covdet.c */; };
		2D1EECD61603E8E900C63DCE /* aib.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D732E7E0CF8C2E40099B03C /* aib.c */; };
		2D1EECD71603E8E900C63DCE /* array.c in Sources */ = {isa = PBXBuildFile; fileRef = 2DFA36D212F1A26D00E808D9 /* array.c */; };
		2D1EECD81603E8E900C63DCE /* dsift.c in Sources */ = {isa = PBXBuildFile; fileRef = 2DE5B37D0FDC2BE9008CEB1D /* dsift.c */; };
		2D1EECD91603E8E900C63DCE /* generic.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D732E800CF8C2E40099B03C /* generic.c */; };
		2D1EECDA1603E8E900C63DCE /* getopt_long.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D732E820CF8C2E40099B03C /* getopt_long.c */; };
		2D1EECDB1603E8E900C63DCE /* hikmeans.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D732E840CF8C2E40099B03C /* hikmeans.c */; };
		2D1EECDC1603E8E900C63DCE /* hog.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D85DE1414CD78BB00BDAE4E /* hog.c */; };
		2D1EECDD1603E8E900C63DCE /* homkermap.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D519081115A800C0079E222 /* homkermap.c */; };
		2D1EECDE1603E8E900C63DCE /* host.c in Sources */ = {isa = PBXBuildFile; fileRef = 2DD99E900E59EA8E00CE1DA1 /* host.c */; };
		2D1EECDF1603E8E900C63DCE /* ikmeans.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D732E860CF8C2E40099B03C /* ikmeans.c */; };
		2D1EECE01603E8E900C63DCE /* imopv.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D72EAF00E48A42F005DAA47 /* imopv.c */; };
		2D1EECE11603E8E900C63DCE /* imopv_sse2.c in Sources */ = {isa = PBXBuildFile; fileRef = 2DD99CC40E58A86B00CE1DA1 /* imopv_sse2.c */; };
		2D1EECE21603E8E900C63DCE /* kdtree.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D765BAD0FEC076700D08578 /* kdtree.c */; };
		2D1EECE31603E8E900C63DCE /* kmeans.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D4EB0BF10F3C1E800ADA534 /* kmeans.c */; };
		2D1EECE41603E8E900C63DCE /* lbp.c in Sources */ = {isa = PBXBuildFile; fileRef = 2DDA2307124BD104003F6A9D /* lbp.c */; };
		2D1EECE51603E8E900C63DCE /* mathop.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D732E8E0CF8C2E40099B03C /* mathop.c */; };
		2D1EECE61603E8E900C63DCE /* mathop_sse2.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D13EEE1100A511200C072E8 /* mathop_sse2.c */; };
		2D1EECE71603E8E900C63DCE /* mser.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D732E900CF8C2E40099B03C /* mser.c */; };
		2D1EECE91603E8E900C63DCE /* pgm.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D732E920CF8C2E40099B03C /* pgm.c */; };
		2D1EECEA1603E8E900C63DCE /* quickshift.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D765BAF0FEC076700D08578 /* quickshift.c */; };
		2D1EECEB1603E8E900C63DCE /* random.c in Sources */ = {isa = PBXBuildFile; fileRef = 2DD302780DE33107009443C7 /* random.c */; };
		2D1EECEC1603E8E900C63DCE /* rodrigues.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D732E940CF8C2E40099B03C /* rodrigues.c */; };
		2D1EECED1603E8E900C63DCE /* sift.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D732E960CF8C2E40099B03C /* sift.c */; };
		2D1EECEE1603E8E900C63DCE /* slic.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D94E6BC148E48440089ADA5 /* slic.c */; };
		2D1EECEF1603E8E900C63DCE /* stringop.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D732E980CF8C2E40099B03C /* stringop.c */; };
		2D1EECF01603E8E900C63DCE /* svmdataset.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D1EECCC1603E1B100C63DCE /* svmdataset.c */; };
		2D1EED1F1604992B00C63DCE /* scalespace.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D1EED1D1604992A00C63DCE /* scalespace.c */; };
		2D1EED201604992B00C63DCE /* scalespace.h in Headers */ = {isa = PBXBuildFile; fileRef = 2D1EED1E1604992A00C63DCE /* scalespace.h */; };
		2D4938AF1895ACE400B775EE /* test_sqrti.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D4938AE1895ACE400B775EE /* test_sqrti.c */; };
		2D63C3A917969032001C6AE0 /* vl_liop.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D9A805D17745CB500B9A9CD /* vl_liop.c */; };
		2D81B9B11735666E000706C0 /* libvl.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 2D1EECD21603E85200C63DCE /* libvl.dylib */; };
		2D81B9B717356697000706C0 /* vl_svmtrain.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D81B9A917351EE5000706C0 /* vl_svmtrain.c */; };
		2DA47BD41792E6C700EC02E0 /* libvl.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 2D1EECD21603E85200C63DCE /* libvl.dylib */; };
		2DA64CD917329C2400276F3D /* fisher.c in Sources */ = {isa = PBXBuildFile; fileRef = 2DA64CD217329C2400276F3D /* fisher.c */; };
		2DA64CDA17329C2400276F3D /* fisher.h in Headers */ = {isa = PBXBuildFile; fileRef = 2DA64CD317329C2400276F3D /* fisher.h */; };
		2DA64CDB17329C2400276F3D /* gmm.c in Sources */ = {isa = PBXBuildFile; fileRef = 2DA64CD417329C2400276F3D /* gmm.c */; };
		2DA64CDC17329C2400276F3D /* gmm.h in Headers */ = {isa = PBXBuildFile; fileRef = 2DA64CD517329C2400276F3D /* gmm.h */; };
		2DA64CDD17329C2400276F3D /* svm.h in Headers */ = {isa = PBXBuildFile; fileRef = 2DA64CD617329C2400276F3D /* svm.h */; };
		2DA64CDE17329C2400276F3D /* vlad.c in Sources */ = {isa = PBXBuildFile; fileRef = 2DA64CD717329C2400276F3D /* vlad.c */; };
		2DA64CDF17329C2400276F3D /* vlad.h in Headers */ = {isa = PBXBuildFile; fileRef = 2DA64CD817329C2400276F3D /* vlad.h */; };
		2DA64CE11733089000276F3D /* svm.c in Sources */ = {isa = PBXBuildFile; fileRef = 2DA64CE01733089000276F3D /* svm.c */; };
		2DB3F05D1605ED2300862CCA /* vl_covdet.c in Sources */ = {isa = PBXBuildFile; fileRef = 2DB3F04F1605DFDB00862CCA /* vl_covdet.c */; };
		2DB3F06B1605F3A100862CCA /* libvl.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 2D1EECD21603E85200C63DCE /* libvl.dylib */; };
		2DD9AD1917F9C41200C1FC78 /* libvl.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 2D1EECD21603E85200C63DCE /* libvl.dylib */; };
		2DD9AD1F17F9C43700C1FC78 /* vl_kmeans.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D1EECFA16046F1B00C63DCE /* vl_kmeans.c */; };
		2DD9AD2017F9C44A00C1FC78 /* vl_fisher.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D141E20178376CA00E2958B /* vl_fisher.c */; };
		2DE607611785722900E1A24E /* libvl.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 2D1EECD21603E85200C63DCE /* libvl.dylib */; };
		2DE607671785725200E1A24E /* vl_vlad.c in Sources */ = {isa = PBXBuildFile; fileRef = 2D141E2B178376CA00E2958B /* vl_vlad.c */; };
		2DFA2399173B7D0F0065603E /* libvl.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 2D1EECD21603E85200C63DCE /* libvl.dylib */; };
		2DFA23A1173B7E730065603E /* vl_gmm.c in Sources */ = {isa = PBXBuildFile; fileRef = 2DFA239F173B7DFB0065603E /* vl_gmm.c */; };
		2DFA23A4173B7F1C0065603E /* liop.c in Sources */ = {isa = PBXBuildFile; fileRef = 2DFA23A2173B7F1C0065603E /* liop.c */; };
		2DFA23A5173B7F1C0065603E /* liop.h in Headers */ = {isa = PBXBuildFile; fileRef = 2DFA23A3173B7F1C0065603E /* liop.h */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		2D0E6C4B1003E0DF00F0864E /* float.th */ = {isa = PBXFileReference; explicitFileType = sourcecode.c.h; fileEncoding = 4; path = float.th; sourceTree = "<group>"; };
		2D0E8A811786D18D005419DC /* vl_fisher.mexmaci64 */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.dylib"; includeInIndex = 0; path = vl_fisher.mexmaci64; sourceTree = BUILT_PRODUCTS_DIR; };
		2D10471316D54C45001677AD /* vl_test_aib.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_test_aib.m; path = toolbox/xtest/vl_test_aib.m; sourceTree = "<group>"; };
		2D10471416D54C45001677AD /* vl_test_alldist2.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_test_alldist2.m; path = toolbox/xtest/vl_test_alldist2.m; sourceTree = "<group>"; };
		2D10471516D54C45001677AD /* vl_test_alphanum.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_test_alphanum.m; path = toolbox/xtest/vl_test_alphanum.m; sourceTree = "<group>"; };
		2D10471616D54C45001677AD /* vl_test_colsubset.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_test_colsubset.m; path = toolbox/xtest/vl_test_colsubset.m; sourceTree = "<group>"; };
		2D10471716D54C45001677AD /* vl_test_cummax.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_test_cummax.m; path = toolbox/xtest/vl_test_cummax.m; sourceTree = "<group>"; };
		2D10471816D54C45001677AD /* vl_test_hikmeans.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_test_hikmeans.m; path = toolbox/xtest/vl_test_hikmeans.m; sourceTree = "<group>"; };
		2D10471916D54C45001677AD /* vl_test_hog.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_test_hog.m; path = toolbox/xtest/vl_test_hog.m; sourceTree = "<group>"; };
		2D10471A16D54C45001677AD /* vl_test_ihashsum.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_test_ihashsum.m; path = toolbox/xtest/vl_test_ihashsum.m; sourceTree = "<group>"; };
		2D10471B16D54C45001677AD /* vl_test_ikmeans.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_test_ikmeans.m; path = toolbox/xtest/vl_test_ikmeans.m; sourceTree = "<group>"; };
		2D10471C16D54C45001677AD /* vl_test_inthist.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_test_inthist.m; path = toolbox/xtest/vl_test_inthist.m; sourceTree = "<group>"; };
		2D10471D16D54C45001677AD /* vl_test_maketrainingset.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_test_maketrainingset.m; path = toolbox/xtest/vl_test_maketrainingset.m; sourceTree = "<group>"; };
		2D10471E16D54C45001677AD /* vl_test_mser.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_test_mser.m; path = toolbox/xtest/vl_test_mser.m; sourceTree = "<group>"; };
		2D10471F16D54C45001677AD /* vl_test_pr.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_test_pr.m; path = toolbox/xtest/vl_test_pr.m; sourceTree = "<group>"; };
		2D10472016D54C45001677AD /* vl_test_roc.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_test_roc.m; path = toolbox/xtest/vl_test_roc.m; sourceTree = "<group>"; };
		2D10472116D54C45001677AD /* vl_test_slic.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_test_slic.m; path = toolbox/xtest/vl_test_slic.m; sourceTree = "<group>"; };
		2D117997178C1EA900311182 /* mathop_avx.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = mathop_avx.c; sourceTree = "<group>"; };
		2D117998178C1EA900311182 /* mathop_avx.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = mathop_avx.h; sourceTree = "<group>"; };
		2D13EEE1100A511200C072E8 /* mathop_sse2.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = mathop_sse2.c; sourceTree = "<group>"; };
		2D13EEE4100A539200C072E8 /* mathop_sse2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = mathop_sse2.h; sourceTree = "<group>"; };
		2D13EEF8100A5CFE00C072E8 /* test_vec_comp.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = test_vec_comp.c; sourceTree = "<group>"; };
		2D13F028100A6E8800C072E8 /* vl_alldist.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_alldist.c; path = toolbox/misc/vl_alldist.c; sourceTree = "<group>"; };
		2D141E1F178376CA00E2958B /* vl_demo_gmm_3d.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_demo_gmm_3d.m; path = toolbox/demo/vl_demo_gmm_3d.m; sourceTree = "<group>"; };
		2D141E20178376CA00E2958B /* vl_fisher.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_fisher.c; path = toolbox/fisher/vl_fisher.c; sourceTree = "<group>"; };
		2D141E21178376CA00E2958B /* vl_demo_gmm_2d_twist.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_demo_gmm_2d_twist.m; path = toolbox/demo/vl_demo_gmm_2d_twist.m; sourceTree = "<group>"; };
		2D141E22178376CA00E2958B /* vl_demo_kmeans_ann_speed.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_demo_kmeans_ann_speed.m; path = toolbox/demo/vl_demo_kmeans_ann_speed.m; sourceTree = "<group>"; };
		2D141E23178376CA00E2958B /* vl_demo_svm.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_demo_svm.m; path = toolbox/demo/vl_demo_svm.m; sourceTree = "<group>"; };
		2D141E24178376CA00E2958B /* vl_test_svmtrain.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_test_svmtrain.m; path = toolbox/xtest/vl_test_svmtrain.m; sourceTree = "<group>"; };
		2D141E25178376CA00E2958B /* vl_fisher.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_fisher.m; path = toolbox/fisher/vl_fisher.m; sourceTree = "<group>"; };
		2D141E26178376CA00E2958B /* vl_frame2oell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_frame2oell.m; path = toolbox/sift/vl_frame2oell.m; sourceTree = "<group>"; };
		2D141E27178376CA00E2958B /* vl_threads.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_threads.m; path = toolbox/misc/vl_threads.m; sourceTree = "<group>"; };
		2D141E28178376CA00E2958B /* vl_svmdataset.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_svmdataset.m; path = toolbox/misc/vl_svmdataset.m; sourceTree = "<group>"; };
		2D141E29178376CA00E2958B /* vl_test_printsize.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_test_printsize.m; path = toolbox/xtest/vl_test_printsize.m; sourceTree = "<group>"; };
		2D141E2B178376CA00E2958B /* vl_vlad.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_vlad.c; path = toolbox/vlad/vl_vlad.c; sourceTree = "<group>"; };
		2D141E2C178376CA00E2958B /* vl_cummax.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_cummax.m; path = toolbox/misc/vl_cummax.m; sourceTree = "<group>"; };
		2D141E2D178376CA00E2958B /* vl_demo_plots_rank.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_demo_plots_rank.m; path = toolbox/demo/vl_demo_plots_rank.m; sourceTree = "<group>"; };
		2D141E2E178376CA00E2958B /* vl_demo_slic.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_demo_slic.m; path = toolbox/demo/vl_demo_slic.m; sourceTree = "<group>"; };
		2D141E2F178376CA00E2958B /* vl_imreadbw.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_imreadbw.m; path = toolbox/imop/vl_imreadbw.m; sourceTree = "<group>"; };
		2D141E30178376CA00E2958B /* vl_demo_covdet.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_demo_covdet.m; path = toolbox/demo/vl_demo_covdet.m; sourceTree = "<group>"; };
		2D141E31178376CA00E2958B /* vl_plotstyle.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_plotstyle.m; path = toolbox/plotop/vl_plotstyle.m; sourceTree = "<group>"; };
		2D141E32178376CA00E2958B /* vl_ikmeans.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_ikmeans.c; path = toolbox/kmeans/vl_ikmeans.c; sourceTree = "<group>"; };
		2D141E33178376CA00E2958B /* vl_det.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_det.m; path = toolbox/plotop/vl_det.m; sourceTree = "<group>"; };
		2D1EECCC1603E1B100C63DCE /* svmdataset.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = svmdataset.c; sourceTree = "<group>"; };
		2D1EECCD1603E1B100C63DCE /* svmdataset.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = svmdataset.h; sourceTree = "<group>"; };
		2D1EECD21603E85200C63DCE /* libvl.dylib */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.dylib"; includeInIndex = 0; path = libvl.dylib; sourceTree = BUILT_PRODUCTS_DIR; };
		2D1EECF116046F1B00C63DCE /* vl_maketrainingset.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = vl_maketrainingset.c; path = toolbox/misc/vl_maketrainingset.c; sourceTree = "<group>"; };
		2D1EECF416046F1B00C63DCE /* vl_ihashsum.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = vl_ihashsum.c; path = toolbox/misc/vl_ihashsum.c; sourceTree = "<group>"; };
		2D1EECF716046F1B00C63DCE /* vl_aib.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = vl_aib.c; path = toolbox/aib/vl_aib.c; sourceTree = "<group>"; };
		2D1EECF916046F1B00C63DCE /* vl_ihashfind.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = vl_ihashfind.c; path = toolbox/misc/vl_ihashfind.c; sourceTree = "<group>"; };
		2D1EECFA16046F1B00C63DCE /* vl_kmeans.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = vl_kmeans.c; path = toolbox/kmeans/vl_kmeans.c; sourceTree = "<group>"; };
		2D1EECFB16046F1C00C63DCE /* vl_sift.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = vl_sift.c; path = toolbox/sift/vl_sift.c; sourceTree = "<group>"; };
		2D1EECFE16046F1C00C63DCE /* vl_alldist.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = vl_alldist.c; path = toolbox/misc/vl_alldist.c; sourceTree = "<group>"; };
		2D1EED0016046F1C00C63DCE /* vl_binsearch.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = vl_binsearch.c; path = toolbox/misc/vl_binsearch.c; sourceTree = "<group>"; };
		2D1EED0216046F1C00C63DCE /* vl_cummax.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = vl_cummax.c; path = toolbox/misc/vl_cummax.c; sourceTree = "<group>"; };
		2D1EED0316046F1C00C63DCE /* vl_dsift.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = vl_dsift.c; path = toolbox/sift/vl_dsift.c; sourceTree = "<group>"; };
		2D1EED0516046F1C00C63DCE /* vl_getpid.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = vl_getpid.c; path = toolbox/misc/vl_getpid.c; sourceTree = "<group>"; };
		2D1EED0616046F1C00C63DCE /* vl_hikmeans.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = vl_hikmeans.c; path = toolbox/kmeans/vl_hikmeans.c; sourceTree = "<group>"; };
		2D1EED0716046F1C00C63DCE /* vl_hikmeanspush.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = vl_hikmeanspush.c; path = toolbox/kmeans/vl_hikmeanspush.c; sourceTree = "<group>"; };
		2D1EED0B16046F1C00C63DCE /* vl_imdisttf.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = vl_imdisttf.c; path = toolbox/imop/vl_imdisttf.c; sourceTree = "<group>"; };
		2D1EED0C16046F1C00C63DCE /* vl_imintegral.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = vl_imintegral.c; path = toolbox/imop/vl_imintegral.c; sourceTree = "<group>"; };
		2D1EED0D16046F1C00C63DCE /* vl_imsmooth.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = vl_imsmooth.c; path = toolbox/imop/vl_imsmooth.c; sourceTree = "<group>"; };
		2D1EED1116046F1C00C63DCE /* vl_kdtreebuild.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = vl_kdtreebuild.c; path = toolbox/misc/vl_kdtreebuild.c; sourceTree = "<group>"; };
		2D1EED1216046F1C00C63DCE /* vl_kdtreequery.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = vl_kdtreequery.c; path = toolbox/misc/vl_kdtreequery.c; sourceTree = "<group>"; };
		2D1EED1816046F1C00C63DCE /* vl_simdctrl.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = vl_simdctrl.c; path = toolbox/misc/vl_simdctrl.c; sourceTree = "<group>"; };
		2D1EED1916046F1C00C63DCE /* vl_tpsumx.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = vl_tpsumx.c; path = toolbox/imop/vl_tpsumx.c; sourceTree = "<group>"; };
		2D1EED1A16046F1C00C63DCE /* vl_twister.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; name = vl_twister.c; path = toolbox/misc/vl_twister.c; sourceTree = "<group>"; };
		2D1EED1D1604992A00C63DCE /* scalespace.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = scalespace.c; sourceTree = "<group>"; };
		2D1EED1E1604992A00C63DCE /* scalespace.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = scalespace.h; sourceTree = "<group>"; };
		2D1EED211604FE5900C63DCE /* covdet.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = covdet.c; sourceTree = "<group>"; };
		2D1EED221604FE5900C63DCE /* covdet.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = covdet.h; sourceTree = "<group>"; };
		2D21907E187099540094BCA2 /* covdet.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = covdet.html; path = docsrc/tutorials/covdet.html; sourceTree = "<group>"; };
		2D21907F187099540094BCA2 /* doxytag.py */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 4; lastKnownFileType = text.script.python; name = doxytag.py; path = docsrc/doxytag.py; sourceTree = "<group>"; tabWidth = 4; };
		2D219080187099540094BCA2 /* gmm.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = gmm.html; path = docsrc/tutorials/gmm.html; sourceTree = "<group>"; };
		2D219081187099540094BCA2 /* hog.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = hog.html; path = docsrc/tutorials/hog.html; sourceTree = "<group>"; };
		2D219082187099540094BCA2 /* install-octave.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = "install-octave.html"; path = "docsrc/install-octave.html"; sourceTree = "<group>"; };
		2D219083187099540094BCA2 /* kmeans.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = kmeans.html; path = docsrc/tutorials/kmeans.html; sourceTree = "<group>"; };
		2D219084187099540094BCA2 /* liop.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = liop.html; path = docsrc/tutorials/liop.html; sourceTree = "<group>"; };
		2D219085187099550094BCA2 /* notfound.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = notfound.html; path = docsrc/notfound.html; sourceTree = "<group>"; };
		2D219086187099550094BCA2 /* roadmap.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = roadmap.html; path = docsrc/roadmap.html; sourceTree = "<group>"; };
		2D219087187099550094BCA2 /* svm.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = svm.html; path = docsrc/tutorials/svm.html; sourceTree = "<group>"; };
		2D219088187099550094BCA2 /* vlfeat-website-main-content.xml */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; name = "vlfeat-website-main-content.xml"; path = "docsrc/vlfeat-website-main-content.xml"; sourceTree = "<group>"; };
		2D219089187099550094BCA2 /* vlfeat-website-preproc.xml */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; name = "vlfeat-website-preproc.xml"; path = "docsrc/vlfeat-website-preproc.xml"; sourceTree = "<group>"; };
		2D21908A187099550094BCA2 /* vlfeat-website-template.xml */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; name = "vlfeat-website-template.xml"; path = "docsrc/vlfeat-website-template.xml"; sourceTree = "<group>"; };
		2D21908B187099550094BCA2 /* vlfeat-website.xml */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xml; name = "vlfeat-website.xml"; path = "docsrc/vlfeat-website.xml"; sourceTree = "<group>"; };
		2D21908C187099550094BCA2 /* vlfeat.css */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.css; name = vlfeat.css; path = docsrc/vlfeat.css; sourceTree = "<group>"; };
		2D21909218709C590094BCA2 /* frame.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = frame.html; path = docsrc/tutorials/frame.html; sourceTree = "<group>"; };
		2D219093187578140094BCA2 /* encode.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = encode.html; path = docsrc/tutorials/encode.html; sourceTree = "<group>"; };
		2D219094187578140094BCA2 /* imdisttf.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = imdisttf.html; path = docsrc/tutorials/imdisttf.html; sourceTree = "<group>"; };
		2D27614810FF6B3900D14D9E /* vl_imdisttf.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_imdisttf.c; path = toolbox/imop/vl_imdisttf.c; sourceTree = "<group>"; };
		2D289FFB16D53FC200E81844 /* svms_common.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = svms_common.h; path = toolbox/misc/svms_common.h; sourceTree = "<group>"; };
		2D289FFC16D53FC200E81844 /* vl_maketrainingset.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_maketrainingset.m; path = toolbox/misc/vl_maketrainingset.m; sourceTree = "<group>"; };
		2D28A00816D5435E00E81844 /* vl_lbp.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_lbp.c; path = toolbox/misc/vl_lbp.c; sourceTree = "<group>"; };
		2D29DD661608F12B00E74DF3 /* test_gauss_elimination.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = test_gauss_elimination.c; sourceTree = "<group>"; };
		2D3E1F2C15FE93480035AA1E /* license.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = license.html; path = docsrc/license.html; sourceTree = "<group>"; };
		2D3E1F2D15FE93480035AA1E /* plots-rank.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = "plots-rank.html"; path = "docsrc/tutorials/plots-rank.html"; sourceTree = "<group>"; };
		2D3E1F2E15FE93480035AA1E /* vlfeat.bib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; name = vlfeat.bib; path = docsrc/vlfeat.bib; sourceTree = "<group>"; };
		2D3E1F2F15FFE6110035AA1E /* vl_hog.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_hog.m; path = toolbox/misc/vl_hog.m; sourceTree = "<group>"; };
		2D3F063417838CF100B27808 /* vl_slic.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_slic.c; path = toolbox/slic/vl_slic.c; sourceTree = "<group>"; };
		2D471AA710DD2C7900FA4182 /* test_threads.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = test_threads.c; sourceTree = "<group>"; };
		2D4938AE1895ACE400B775EE /* test_sqrti.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = test_sqrti.c; sourceTree = "<group>"; };
		2D4938B01895BAAA00B775EE /* vl_demo_ikmeans.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_demo_ikmeans.m; path = toolbox/demo/vl_demo_ikmeans.m; sourceTree = "<group>"; };
		2D4EB0BF10F3C1E800ADA534 /* kmeans.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = kmeans.c; sourceTree = "<group>"; };
		2D4EB0C010F3C1E800ADA534 /* kmeans.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = kmeans.h; sourceTree = "<group>"; };
		2D4EB14D10F3F3A500ADA534 /* shuffle-def.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "shuffle-def.h"; sourceTree = "<group>"; };
		2D519080115A800C0079E222 /* homkermap.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = homkermap.h; sourceTree = "<group>"; };
		2D519081115A800C0079E222 /* homkermap.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = homkermap.c; sourceTree = "<group>"; };
		2D6007FF1115C1FA0020963C /* check.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = check.h; sourceTree = "<group>"; };
		2D6102D0111EE22C00470F4C /* bin.mak */ = {isa = PBXFileReference; explicitFileType = sourcecode.make; fileEncoding = 4; indentWidth = 8; path = bin.mak; sourceTree = "<group>"; tabWidth = 8; };
		2D6102D1111EE22C00470F4C /* dist.mak */ = {isa = PBXFileReference; explicitFileType = sourcecode.make; fileEncoding = 4; indentWidth = 8; path = dist.mak; sourceTree = "<group>"; tabWidth = 8; };
		2D6102D2111EE22C00470F4C /* dll.mak */ = {isa = PBXFileReference; explicitFileType = sourcecode.make; fileEncoding = 4; indentWidth = 8; path = dll.mak; sourceTree = "<group>"; tabWidth = 8; };
		2D6102D3111EE22C00470F4C /* doc.mak */ = {isa = PBXFileReference; explicitFileType = sourcecode.make; fileEncoding = 4; indentWidth = 8; path = doc.mak; sourceTree = "<group>"; tabWidth = 8; };
		2D6102D4111EE22C00470F4C /* matlab.mak */ = {isa = PBXFileReference; explicitFileType = sourcecode.make; fileEncoding = 4; indentWidth = 8; path = matlab.mak; sourceTree = "<group>"; tabWidth = 8; };
		2D6102D5111EE22C00470F4C /* octave.mak */ = {isa = PBXFileReference; explicitFileType = sourcecode.make; fileEncoding = 4; indentWidth = 8; path = octave.mak; sourceTree = "<group>"; tabWidth = 8; };
		2D624AD60FF9306700DB3122 /* test_heap-def.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "test_heap-def.c"; sourceTree = "<group>"; };
		2D66AC5D13AE05BD0060594E /* vl_aib.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_aib.m; path = toolbox/aib/vl_aib.m; sourceTree = "<group>"; };
		2D66AC5E13AE05BD0060594E /* vl_aibcut.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_aibcut.m; path = toolbox/aib/vl_aibcut.m; sourceTree = "<group>"; };
		2D66AC5F13AE05BD0060594E /* vl_aibcuthist.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_aibcuthist.m; path = toolbox/aib/vl_aibcuthist.m; sourceTree = "<group>"; };
		2D66AC6013AE05BD0060594E /* vl_aibcutpush.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_aibcutpush.m; path = toolbox/aib/vl_aibcutpush.m; sourceTree = "<group>"; };
		2D66AC6113AE05BD0060594E /* vl_aibhist.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_aibhist.m; path = toolbox/aib/vl_aibhist.m; sourceTree = "<group>"; };
		2D66AC6213AE05BD0060594E /* vl_alldist2.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_alldist2.m; path = toolbox/misc/vl_alldist2.m; sourceTree = "<group>"; };
		2D66AC6313AE05BD0060594E /* vl_argparse.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_argparse.m; path = toolbox/misc/vl_argparse.m; sourceTree = "<group>"; };
		2D66AC6413AE05BD0060594E /* vl_assert_almost_equal.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_assert_almost_equal.m; path = toolbox/xtest/vl_assert_almost_equal.m; sourceTree = "<group>"; };
		2D66AC6513AE05BD0060594E /* vl_assert_equal.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_assert_equal.m; path = toolbox/xtest/vl_assert_equal.m; sourceTree = "<group>"; };
		2D66AC6613AE05BD0060594E /* vl_assert_exception.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_assert_exception.m; path = toolbox/xtest/vl_assert_exception.m; sourceTree = "<group>"; };
		2D66AC6713AE05BD0060594E /* vl_binsearch.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_binsearch.m; path = toolbox/misc/vl_binsearch.m; sourceTree = "<group>"; };
		2D66AC6813AE05BD0060594E /* vl_binsum.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_binsum.m; path = toolbox/misc/vl_binsum.m; sourceTree = "<group>"; };
		2D66AC6913AE05BD0060594E /* vl_cf.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_cf.m; path = toolbox/plotop/vl_cf.m; sourceTree = "<group>"; };
		2D66AC6A13AE05BD0060594E /* vl_click.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_click.m; path = toolbox/plotop/vl_click.m; sourceTree = "<group>"; };
		2D66AC6B13AE05BD0060594E /* vl_clickpoint.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_clickpoint.m; path = toolbox/plotop/vl_clickpoint.m; sourceTree = "<group>"; };
		2D66AC6C13AE05BD0060594E /* vl_clicksegment.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_clicksegment.m; path = toolbox/plotop/vl_clicksegment.m; sourceTree = "<group>"; };
		2D66AC6D13AE05BD0060594E /* vl_colsubset.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_colsubset.m; path = toolbox/misc/vl_colsubset.m; sourceTree = "<group>"; };
		2D66AC6E13AE05BD0060594E /* vl_compile.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_compile.m; path = toolbox/vl_compile.m; sourceTree = "<group>"; };
		2D66AC6F13AE05BD0060594E /* vl_ddgaussian.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_ddgaussian.m; path = toolbox/special/vl_ddgaussian.m; sourceTree = "<group>"; };
		2D66AC7013AE05BD0060594E /* vl_demo_aib.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_demo_aib.m; path = toolbox/demo/vl_demo_aib.m; sourceTree = "<group>"; };
		2D66AC7113AE05BD0060594E /* vl_demo_alldist.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_demo_alldist.m; path = toolbox/demo/vl_demo_alldist.m; sourceTree = "<group>"; };
		2D66AC7213AE05BD0060594E /* vl_demo_cmd.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_demo_cmd.m; path = toolbox/demo/vl_demo_cmd.m; sourceTree = "<group>"; };
		2D66AC7313AE05BD0060594E /* vl_demo_dsift.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_demo_dsift.m; path = toolbox/demo/vl_demo_dsift.m; sourceTree = "<group>"; };
		2D66AC7413AE05BD0060594E /* vl_demo_imdisttf.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_demo_imdisttf.m; path = toolbox/demo/vl_demo_imdisttf.m; sourceTree = "<group>"; };
		2D66AC7513AE05BD0060594E /* vl_demo_kdtree_ann.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_demo_kdtree_ann.m; path = toolbox/demo/vl_demo_kdtree_ann.m; sourceTree = "<group>"; };
		2D66AC7613AE05BD0060594E /* vl_demo_kdtree_forest.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_demo_kdtree_forest.m; path = toolbox/demo/vl_demo_kdtree_forest.m; sourceTree = "<group>"; };
		2D66AC7713AE05BD0060594E /* vl_demo_kdtree_plot.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_demo_kdtree_plot.m; path = toolbox/demo/vl_demo_kdtree_plot.m; sourceTree = "<group>"; };
		2D66AC7813AE05BD0060594E /* vl_demo_kdtree_self.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_demo_kdtree_self.m; path = toolbox/demo/vl_demo_kdtree_self.m; sourceTree = "<group>"; };
		2D66AC7913AE05BD0060594E /* vl_demo_kdtree_sift.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_demo_kdtree_sift.m; path = toolbox/demo/vl_demo_kdtree_sift.m; sourceTree = "<group>"; };
		2D66AC7A13AE05BD0060594E /* vl_demo_kdtree.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_demo_kdtree.m; path = toolbox/demo/vl_demo_kdtree.m; sourceTree = "<group>"; };
		2D66AC7B13AE05BD0060594E /* vl_demo_kmeans_init.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_demo_kmeans_init.m; path = toolbox/demo/vl_demo_kmeans_init.m; sourceTree = "<group>"; };
		2D66AC7C13AE05BD0060594E /* vl_demo_kmeans_vs_builtin.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_demo_kmeans_vs_builtin.m; path = toolbox/demo/vl_demo_kmeans_vs_builtin.m; sourceTree = "<group>"; };
		2D66AC7D13AE05BD0060594E /* vl_demo_mser_basic.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_demo_mser_basic.m; path = toolbox/demo/vl_demo_mser_basic.m; sourceTree = "<group>"; };
		2D66AC7E13AE05BD0060594E /* vl_demo_mser_cmd.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_demo_mser_cmd.m; path = toolbox/demo/vl_demo_mser_cmd.m; sourceTree = "<group>"; };
		2D66AC7F13AE05BD0060594E /* vl_demo_mser_delta.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_demo_mser_delta.m; path = toolbox/demo/vl_demo_mser_delta.m; sourceTree = "<group>"; };
		2D66AC8013AE05BD0060594E /* vl_demo_print.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_demo_print.m; path = toolbox/demo/vl_demo_print.m; sourceTree = "<group>"; };
		2D66AC8113AE05BD0060594E /* vl_demo_quickshift.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_demo_quickshift.m; path = toolbox/demo/vl_demo_quickshift.m; sourceTree = "<group>"; };
		2D66AC8213AE05BD0060594E /* vl_demo_sift_basic.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_demo_sift_basic.m; path = toolbox/demo/vl_demo_sift_basic.m; sourceTree = "<group>"; };
		2D66AC8313AE05BD0060594E /* vl_demo_sift_cmd.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_demo_sift_cmd.m; path = toolbox/demo/vl_demo_sift_cmd.m; sourceTree = "<group>"; };
		2D66AC8413AE05BD0060594E /* vl_demo_sift_edge.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_demo_sift_edge.m; path = toolbox/demo/vl_demo_sift_edge.m; sourceTree = "<group>"; };
		2D66AC8513AE05BD0060594E /* vl_demo_sift_match.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_demo_sift_match.m; path = toolbox/demo/vl_demo_sift_match.m; sourceTree = "<group>"; };
		2D66AC8613AE05BD0060594E /* vl_demo_sift_or.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_demo_sift_or.m; path = toolbox/demo/vl_demo_sift_or.m; sourceTree = "<group>"; };
		2D66AC8713AE05BD0060594E /* vl_demo_sift_peak.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_demo_sift_peak.m; path = toolbox/demo/vl_demo_sift_peak.m; sourceTree = "<group>"; };
		2D66AC8813AE05BD0060594E /* vl_demo_sift_vs_ubc.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_demo_sift_vs_ubc.m; path = toolbox/demo/vl_demo_sift_vs_ubc.m; sourceTree = "<group>"; };
		2D66AC8913AE05BD0060594E /* vl_demo.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_demo.m; path = toolbox/vl_demo.m; sourceTree = "<group>"; };
		2D66AC8A13AE05BD0060594E /* vl_dgaussian.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_dgaussian.m; path = toolbox/special/vl_dgaussian.m; sourceTree = "<group>"; };
		2D66AC8B13AE05BD0060594E /* vl_dsift.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_dsift.m; path = toolbox/sift/vl_dsift.m; sourceTree = "<group>"; };
		2D66AC8C13AE05BD0060594E /* vl_dsigmoid.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_dsigmoid.m; path = toolbox/special/vl_dsigmoid.m; sourceTree = "<group>"; };
		2D66AC8D13AE05BD0060594E /* vl_dwaffine.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_dwaffine.m; path = toolbox/imop/vl_dwaffine.m; sourceTree = "<group>"; };
		2D66AC8E13AE05BD0060594E /* vl_erfill.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_erfill.m; path = toolbox/mser/vl_erfill.m; sourceTree = "<group>"; };
		2D66AC8F13AE05BD0060594E /* vl_ertr.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_ertr.m; path = toolbox/mser/vl_ertr.m; sourceTree = "<group>"; };
		2D66AC9013AE05BD0060594E /* vl_figaspect.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_figaspect.m; path = toolbox/plotop/vl_figaspect.m; sourceTree = "<group>"; };
		2D66AC9113AE05BD0060594E /* vl_flatmap.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_flatmap.m; path = toolbox/quickshift/vl_flatmap.m; sourceTree = "<group>"; };
		2D66AC9213AE05BD0060594E /* vl_gaussian.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_gaussian.m; path = toolbox/special/vl_gaussian.m; sourceTree = "<group>"; };
		2D66AC9313AE05BD0060594E /* vl_getpid.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_getpid.m; path = toolbox/misc/vl_getpid.m; sourceTree = "<group>"; };
		2D66AC9413AE05BD0060594E /* vl_grad.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_grad.m; path = toolbox/misc/vl_grad.m; sourceTree = "<group>"; };
		2D66AC9513AE05BD0060594E /* vl_harris.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_harris.m; path = toolbox/vl_harris.m; sourceTree = "<group>"; };
		2D66AC9613AE05BD0060594E /* vl_hat.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_hat.m; path = toolbox/geometry/vl_hat.m; sourceTree = "<group>"; };
		2D66AC9713AE05BD0060594E /* vl_help.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_help.m; path = toolbox/vl_help.m; sourceTree = "<group>"; };
		2D66AC9813AE05BD0060594E /* vl_hikmeans.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_hikmeans.m; path = toolbox/kmeans/vl_hikmeans.m; sourceTree = "<group>"; };
		2D66AC9913AE05BD0060594E /* vl_hikmeanshist.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_hikmeanshist.m; path = toolbox/kmeans/vl_hikmeanshist.m; sourceTree = "<group>"; };
		2D66AC9A13AE05BD0060594E /* vl_hikmeanspush.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_hikmeanspush.m; path = toolbox/kmeans/vl_hikmeanspush.m; sourceTree = "<group>"; };
		2D66AC9B13AE05BD0060594E /* vl_histmarg.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_histmarg.m; path = toolbox/misc/vl_histmarg.m; sourceTree = "<group>"; };
		2D66AC9C13AE05BD0060594E /* vl_homkermap.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_homkermap.m; path = toolbox/misc/vl_homkermap.m; sourceTree = "<group>"; };
		2D66AC9D13AE05BD0060594E /* vl_ihashfind.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_ihashfind.m; path = toolbox/misc/vl_ihashfind.m; sourceTree = "<group>"; };
		2D66AC9E13AE05BD0060594E /* vl_ihashsum.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_ihashsum.m; path = toolbox/misc/vl_ihashsum.m; sourceTree = "<group>"; };
		2D66AC9F13AE05BD0060594E /* vl_ihat.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_ihat.m; path = toolbox/geometry/vl_ihat.m; sourceTree = "<group>"; };
		2D66ACA013AE05BD0060594E /* vl_ikmeans.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_ikmeans.m; path = toolbox/kmeans/vl_ikmeans.m; sourceTree = "<group>"; };
		2D66ACA113AE05BD0060594E /* vl_ikmeanshist.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_ikmeanshist.m; path = toolbox/kmeans/vl_ikmeanshist.m; sourceTree = "<group>"; };
		2D66ACA213AE05BD0060594E /* vl_ikmeanspush.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_ikmeanspush.m; path = toolbox/kmeans/vl_ikmeanspush.m; sourceTree = "<group>"; };
		2D66ACA313AE05BD0060594E /* vl_imarray.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_imarray.m; path = toolbox/imop/vl_imarray.m; sourceTree = "<group>"; };
		2D66ACA413AE05BD0060594E /* vl_imarraysc.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_imarraysc.m; path = toolbox/imop/vl_imarraysc.m; sourceTree = "<group>"; };
		2D66ACA513AE05BD0060594E /* vl_imdisttf.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_imdisttf.m; path = toolbox/imop/vl_imdisttf.m; sourceTree = "<group>"; };
		2D66ACA613AE05BD0060594E /* vl_imdown.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_imdown.m; path = toolbox/imop/vl_imdown.m; sourceTree = "<group>"; };
		2D66ACA713AE05BD0060594E /* vl_imgrad.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_imgrad.m; path = toolbox/imop/vl_imgrad.m; sourceTree = "<group>"; };
		2D66ACA813AE05BD0060594E /* vl_imintegral.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_imintegral.m; path = toolbox/imop/vl_imintegral.m; sourceTree = "<group>"; };
		2D66ACA913AE05BD0060594E /* vl_imreadgray.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_imreadgray.m; path = toolbox/imop/vl_imreadgray.m; sourceTree = "<group>"; };
		2D66ACAA13AE05BD0060594E /* vl_imsc.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_imsc.m; path = toolbox/imop/vl_imsc.m; sourceTree = "<group>"; };
		2D66ACAB13AE05BD0060594E /* vl_imseg.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_imseg.m; path = toolbox/quickshift/vl_imseg.m; sourceTree = "<group>"; };
		2D66ACAC13AE05BD0060594E /* vl_imsmooth.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_imsmooth.m; path = toolbox/imop/vl_imsmooth.m; sourceTree = "<group>"; };
		2D66ACAE13AE05BD0060594E /* vl_imup.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_imup.m; path = toolbox/imop/vl_imup.m; sourceTree = "<group>"; };
		2D66ACAF13AE05BD0060594E /* vl_imwbackward.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_imwbackward.m; path = toolbox/imop/vl_imwbackward.m; sourceTree = "<group>"; };
		2D66ACB013AE05BD0060594E /* vl_imwhiten.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_imwhiten.m; path = toolbox/imop/vl_imwhiten.m; sourceTree = "<group>"; };
		2D66ACB113AE05BD0060594E /* vl_inthist.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_inthist.m; path = toolbox/misc/vl_inthist.m; sourceTree = "<group>"; };
		2D66ACB213AE05BD0060594E /* vl_irodr.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_irodr.m; path = toolbox/geometry/vl_irodr.m; sourceTree = "<group>"; };
		2D66ACB313AE05BD0060594E /* vl_kdtreebuild.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_kdtreebuild.m; path = toolbox/misc/vl_kdtreebuild.m; sourceTree = "<group>"; };
		2D66ACB413AE05BD0060594E /* vl_kdtreequery.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_kdtreequery.m; path = toolbox/misc/vl_kdtreequery.m; sourceTree = "<group>"; };
		2D66ACB513AE05BD0060594E /* vl_kmeans.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_kmeans.m; path = toolbox/kmeans/vl_kmeans.m; sourceTree = "<group>"; };
		2D66ACB613AE05BD0060594E /* vl_lbp.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_lbp.m; path = toolbox/misc/vl_lbp.m; sourceTree = "<group>"; };
		2D66ACB713AE05BD0060594E /* vl_lbpfliplr.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_lbpfliplr.m; path = toolbox/misc/vl_lbpfliplr.m; sourceTree = "<group>"; };
		2D66ACB813AE05BD0060594E /* vl_linespec2prop.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_linespec2prop.m; path = toolbox/plotop/vl_linespec2prop.m; sourceTree = "<group>"; };
		2D66ACB913AE05BD0060594E /* vl_localmax.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_localmax.m; path = toolbox/misc/vl_localmax.m; sourceTree = "<group>"; };
		2D66ACBA13AE05BD0060594E /* vl_mser.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_mser.m; path = toolbox/mser/vl_mser.m; sourceTree = "<group>"; };
		2D66ACBB13AE05BD0060594E /* vl_noprefix.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_noprefix.m; path = toolbox/vl_noprefix.m; sourceTree = "<group>"; };
		2D66ACBC13AE05BD0060594E /* vl_numder.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_numder.m; path = toolbox/misc/vl_numder.m; sourceTree = "<group>"; };
		2D66ACBD13AE05BD0060594E /* vl_numder2.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_numder2.m; path = toolbox/misc/vl_numder2.m; sourceTree = "<group>"; };
		2D66ACBE13AE05BD0060594E /* vl_override.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_override.m; path = toolbox/misc/vl_override.m; sourceTree = "<group>"; };
		2D66ACC013AE05BD0060594E /* vl_phow.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_phow.m; path = toolbox/sift/vl_phow.m; sourceTree = "<group>"; };
		2D66ACC113AE05BD0060594E /* vl_plotframe.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_plotframe.m; path = toolbox/plotop/vl_plotframe.m; sourceTree = "<group>"; };
		2D66ACC213AE05BD0060594E /* vl_plotgrid.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_plotgrid.m; path = toolbox/plotop/vl_plotgrid.m; sourceTree = "<group>"; };
		2D66ACC313AE05BD0060594E /* vl_plotpoint.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_plotpoint.m; path = toolbox/plotop/vl_plotpoint.m; sourceTree = "<group>"; };
		2D66ACC413AE05BD0060594E /* vl_plotsiftdescriptor.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_plotsiftdescriptor.m; path = toolbox/sift/vl_plotsiftdescriptor.m; sourceTree = "<group>"; };
		2D66ACC513AE05BD0060594E /* vl_pr.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_pr.m; path = toolbox/plotop/vl_pr.m; sourceTree = "<group>"; };
		2D66ACC613AE05BD0060594E /* vl_printsize.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_printsize.m; path = toolbox/plotop/vl_printsize.m; sourceTree = "<group>"; };
		2D66ACC713AE05BD0060594E /* vl_quickseg.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_quickseg.m; path = toolbox/quickshift/vl_quickseg.m; sourceTree = "<group>"; };
		2D66ACC813AE05BD0060594E /* vl_quickshift.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_quickshift.m; path = toolbox/quickshift/vl_quickshift.m; sourceTree = "<group>"; };
		2D66ACC913AE05BD0060594E /* vl_quickvis.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_quickvis.m; path = toolbox/quickshift/vl_quickvis.m; sourceTree = "<group>"; };
		2D66ACCA13AE05BD0060594E /* vl_rcos.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_rcos.m; path = toolbox/special/vl_rcos.m; sourceTree = "<group>"; };
		2D66ACCB13AE05BD0060594E /* vl_rgb2xyz.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_rgb2xyz.m; path = toolbox/imop/vl_rgb2xyz.m; sourceTree = "<group>"; };
		2D66ACCC13AE05BD0060594E /* vl_roc.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_roc.m; path = toolbox/plotop/vl_roc.m; sourceTree = "<group>"; };
		2D66ACCD13AE05BD0060594E /* vl_rodr.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_rodr.m; path = toolbox/geometry/vl_rodr.m; sourceTree = "<group>"; };
		2D66ACCE13AE05BD0060594E /* vl_root.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_root.m; path = toolbox/vl_root.m; sourceTree = "<group>"; };
		2D66ACCF13AE05BD0060594E /* vl_sampleinthist.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_sampleinthist.m; path = toolbox/misc/vl_sampleinthist.m; sourceTree = "<group>"; };
		2D66ACD013AE05BD0060594E /* vl_setup.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_setup.m; path = toolbox/vl_setup.m; sourceTree = "<group>"; };
		2D66ACD113AE05BD0060594E /* vl_sift.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_sift.m; path = toolbox/sift/vl_sift.m; sourceTree = "<group>"; };
		2D66ACD213AE05BD0060594E /* vl_siftdescriptor.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_siftdescriptor.m; path = toolbox/sift/vl_siftdescriptor.m; sourceTree = "<group>"; };
		2D66ACD313AE05BD0060594E /* vl_sigmoid.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_sigmoid.m; path = toolbox/special/vl_sigmoid.m; sourceTree = "<group>"; };
		2D66ACD413AE05BD0060594E /* vl_simdctrl.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_simdctrl.m; path = toolbox/misc/vl_simdctrl.m; sourceTree = "<group>"; };
		2D66ACD613AE05BD0060594E /* vl_test_alldist.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_test_alldist.m; path = toolbox/xtest/vl_test_alldist.m; sourceTree = "<group>"; };
		2D66ACD913AE05BD0060594E /* vl_test_argparse.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_test_argparse.m; path = toolbox/xtest/vl_test_argparse.m; sourceTree = "<group>"; };
		2D66ACDB13AE05BD0060594E /* vl_test_binsearch.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_test_binsearch.m; path = toolbox/xtest/vl_test_binsearch.m; sourceTree = "<group>"; };
		2D66ACDD13AE05BD0060594E /* vl_test_binsum.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_test_binsum.m; path = toolbox/xtest/vl_test_binsum.m; sourceTree = "<group>"; };
		2D66ACE013AE05BD0060594E /* vl_test_dsift.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_test_dsift.m; path = toolbox/xtest/vl_test_dsift.m; sourceTree = "<group>"; };
		2D66ACE113AE05BD0060594E /* vl_test_grad.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_test_grad.m; path = toolbox/xtest/vl_test_grad.m; sourceTree = "<group>"; };
		2D66ACE313AE05BD0060594E /* vl_test_homkermap.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_test_homkermap.m; path = toolbox/xtest/vl_test_homkermap.m; sourceTree = "<group>"; };
		2D66ACE713AE05BD0060594E /* vl_test_imarray.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_test_imarray.m; path = toolbox/xtest/vl_test_imarray.m; sourceTree = "<group>"; };
		2D66ACE813AE05BD0060594E /* vl_test_imdisttf.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_test_imdisttf.m; path = toolbox/xtest/vl_test_imdisttf.m; sourceTree = "<group>"; };
		2D66ACEA13AE05BD0060594E /* vl_test_imintegral.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_test_imintegral.m; path = toolbox/xtest/vl_test_imintegral.m; sourceTree = "<group>"; };
		2D66ACEC13AE05BD0060594E /* vl_test_imsmooth.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_test_imsmooth.m; path = toolbox/xtest/vl_test_imsmooth.m; sourceTree = "<group>"; };
		2D66ACED13AE05BD0060594E /* vl_test_imwbackward.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_test_imwbackward.m; path = toolbox/xtest/vl_test_imwbackward.m; sourceTree = "<group>"; };
		2D66ACEF13AE05BD0060594E /* vl_test_init.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_test_init.m; path = toolbox/xtest/vl_test_init.m; sourceTree = "<group>"; };
		2D66ACF113AE05BD0060594E /* vl_test_kdtree.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_test_kdtree.m; path = toolbox/xtest/vl_test_kdtree.m; sourceTree = "<group>"; };
		2D66ACF213AE05BD0060594E /* vl_test_kmeans.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_test_kmeans.m; path = toolbox/xtest/vl_test_kmeans.m; sourceTree = "<group>"; };
		2D66ACF313AE05BD0060594E /* vl_test_lbp.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_test_lbp.m; path = toolbox/xtest/vl_test_lbp.m; sourceTree = "<group>"; };
		2D66ACF713AE05BD0060594E /* vl_test_phow.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_test_phow.m; path = toolbox/xtest/vl_test_phow.m; sourceTree = "<group>"; };
		2D66ACF913AE05BD0060594E /* vl_test_sift.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_test_sift.m; path = toolbox/xtest/vl_test_sift.m; sourceTree = "<group>"; };
		2D66ACFB13AE05BD0060594E /* vl_test_twister.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_test_twister.m; path = toolbox/xtest/vl_test_twister.m; sourceTree = "<group>"; };
		2D66ACFC13AE05BD0060594E /* vl_test_whistc.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_test_whistc.m; path = toolbox/xtest/vl_test_whistc.m; sourceTree = "<group>"; };
		2D66ACFD13AE05BD0060594E /* vl_test.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_test.m; path = toolbox/xtest/vl_test.m; sourceTree = "<group>"; };
		2D66ACFE13AE05BD0060594E /* vl_tightsubplot.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_tightsubplot.m; path = toolbox/plotop/vl_tightsubplot.m; sourceTree = "<group>"; };
		2D66ACFF13AE05BD0060594E /* vl_tps.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_tps.m; path = toolbox/imop/vl_tps.m; sourceTree = "<group>"; };
		2D66AD0013AE05BD0060594E /* vl_tpsu.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_tpsu.m; path = toolbox/imop/vl_tpsu.m; sourceTree = "<group>"; };
		2D66AD0313AE05BD0060594E /* vl_twister.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_twister.m; path = toolbox/misc/vl_twister.m; sourceTree = "<group>"; };
		2D66AD0413AE05BD0060594E /* vl_ubcmatch.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_ubcmatch.m; path = toolbox/sift/vl_ubcmatch.m; sourceTree = "<group>"; };
		2D66AD0513AE05BD0060594E /* vl_ubcread.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_ubcread.m; path = toolbox/sift/vl_ubcread.m; sourceTree = "<group>"; };
		2D66AD0613AE05BD0060594E /* vl_version.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_version.m; path = toolbox/misc/vl_version.m; sourceTree = "<group>"; };
		2D66AD0713AE05BD0060594E /* vl_waffine.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_waffine.m; path = toolbox/imop/vl_waffine.m; sourceTree = "<group>"; };
		2D66AD0813AE05BD0060594E /* vl_whistc.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_whistc.m; path = toolbox/misc/vl_whistc.m; sourceTree = "<group>"; };
		2D66AD0913AE05BD0060594E /* vl_witps.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_witps.m; path = toolbox/imop/vl_witps.m; sourceTree = "<group>"; };
		2D66AD0A13AE05BD0060594E /* vl_wtps.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_wtps.m; path = toolbox/imop/vl_wtps.m; sourceTree = "<group>"; };
		2D66AD0B13AE05BD0060594E /* vl_xmkdir.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_xmkdir.m; path = toolbox/misc/vl_xmkdir.m; sourceTree = "<group>"; };
		2D66AD0C13AE05BD0060594E /* vl_xyz2lab.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_xyz2lab.m; path = toolbox/imop/vl_xyz2lab.m; sourceTree = "<group>"; };
		2D66AD0D13AE05BD0060594E /* vl_xyz2luv.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_xyz2luv.m; path = toolbox/imop/vl_xyz2luv.m; sourceTree = "<group>"; };
		2D66AD0E13AE05BD0060594E /* vl_xyz2rgb.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_xyz2rgb.m; path = toolbox/imop/vl_xyz2rgb.m; sourceTree = "<group>"; };
		2D6DD0F4100F5E5E006AE152 /* test_mathop_abs.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = test_mathop_abs.c; sourceTree = "<group>"; };
		2D6E381A149CE4B5005319B7 /* vl_slic.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_slic.m; path = toolbox/slic/vl_slic.m; sourceTree = "<group>"; };
		2D6E381B149CE4ED005319B7 /* slic.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = slic.html; path = docsrc/tutorials/slic.html; sourceTree = "<group>"; };
		2D708F261493C5540079605B /* doxygen.css */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.css; name = doxygen.css; path = docsrc/doxygen.css; sourceTree = "<group>"; };
		2D70CC780DDE1135000A23DE /* mexutils.h */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 2; lastKnownFileType = sourcecode.c.h; name = mexutils.h; path = toolbox/mexutils.h; sourceTree = "<group>"; tabWidth = 2; };
		2D72EAF00E48A42F005DAA47 /* imopv.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = imopv.c; sourceTree = "<group>"; };
		2D72EAF10E48A42F005DAA47 /* imopv.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = imopv.h; sourceTree = "<group>"; };
		2D72EB080E48A934005DAA47 /* test_imopv.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = test_imopv.c; sourceTree = "<group>"; };
		2D732DE30CF8C2CB0099B03C /* Makefile */ = {isa = PBXFileReference; explicitFileType = sourcecode.make; fileEncoding = 4; indentWidth = 8; path = Makefile; sourceTree = "<group>"; tabWidth = 8; usesTabs = 1; };
		2D732DE50CF8C2E40099B03C /* aib.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = aib.c; sourceTree = "<group>"; };
		2D732DE60CF8C2E40099B03C /* generic-driver.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "generic-driver.h"; sourceTree = "<group>"; };
		2D732DE80CF8C2E40099B03C /* mser.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = mser.c; sourceTree = "<group>"; };
		2D732DEA0CF8C2E40099B03C /* sift.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = sift.c; sourceTree = "<group>"; };
		2D732DEB0CF8C2E40099B03C /* test_getopt_long.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = test_getopt_long.c; sourceTree = "<group>"; };
		2D732DEC0CF8C2E40099B03C /* test_nan.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = test_nan.c; sourceTree = "<group>"; };
		2D732DED0CF8C2E40099B03C /* test_stringop.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = test_stringop.c; sourceTree = "<group>"; };
		2D732E7E0CF8C2E40099B03C /* aib.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = aib.c; sourceTree = "<group>"; };
		2D732E7F0CF8C2E40099B03C /* aib.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = aib.h; sourceTree = "<group>"; };
		2D732E800CF8C2E40099B03C /* generic.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = generic.c; sourceTree = "<group>"; };
		2D732E810CF8C2E40099B03C /* generic.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = generic.h; sourceTree = "<group>"; };
		2D732E820CF8C2E40099B03C /* getopt_long.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = getopt_long.c; sourceTree = "<group>"; };
		2D732E830CF8C2E40099B03C /* getopt_long.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = getopt_long.h; sourceTree = "<group>"; };
		2D732E840CF8C2E40099B03C /* hikmeans.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = hikmeans.c; sourceTree = "<group>"; };
		2D732E850CF8C2E40099B03C /* hikmeans.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = hikmeans.h; sourceTree = "<group>"; };
		2D732E860CF8C2E40099B03C /* ikmeans.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = ikmeans.c; sourceTree = "<group>"; };
		2D732E870CF8C2E40099B03C /* ikmeans.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ikmeans.h; sourceTree = "<group>"; };
		2D732E880CF8C2E40099B03C /* ikmeans_elkan.tc */ = {isa = PBXFileReference; explicitFileType = sourcecode.c; fileEncoding = 4; includeInIndex = 1; path = ikmeans_elkan.tc; sourceTree = "<group>"; };
		2D732E890CF8C2E40099B03C /* ikmeans_init.tc */ = {isa = PBXFileReference; explicitFileType = sourcecode.c; fileEncoding = 4; path = ikmeans_init.tc; sourceTree = "<group>"; };
		2D732E8A0CF8C2E40099B03C /* ikmeans_lloyd.tc */ = {isa = PBXFileReference; explicitFileType = sourcecode.c; fileEncoding = 4; path = ikmeans_lloyd.tc; sourceTree = "<group>"; };
		2D732E8E0CF8C2E40099B03C /* mathop.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = mathop.c; sourceTree = "<group>"; };
		2D732E8F0CF8C2E40099B03C /* mathop.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = mathop.h; sourceTree = "<group>"; };
		2D732E900CF8C2E40099B03C /* mser.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = mser.c; sourceTree = "<group>"; };
		2D732E910CF8C2E40099B03C /* mser.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = mser.h; sourceTree = "<group>"; };
		2D732E920CF8C2E40099B03C /* pgm.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = pgm.c; sourceTree = "<group>"; };
		2D732E930CF8C2E40099B03C /* pgm.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = pgm.h; sourceTree = "<group>"; };
		2D732E940CF8C2E40099B03C /* rodrigues.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = rodrigues.c; sourceTree = "<group>"; };
		2D732E950CF8C2E40099B03C /* rodrigues.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = rodrigues.h; sourceTree = "<group>"; };
		2D732E960CF8C2E40099B03C /* sift.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = sift.c; sourceTree = "<group>"; };
		2D732E970CF8C2E40099B03C /* sift.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = sift.h; sourceTree = "<group>"; };
		2D732E980CF8C2E40099B03C /* stringop.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = stringop.c; sourceTree = "<group>"; };
		2D732E990CF8C2E40099B03C /* stringop.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = stringop.h; sourceTree = "<group>"; };
		2D765BAD0FEC076700D08578 /* kdtree.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = kdtree.c; sourceTree = "<group>"; };
		2D765BAE0FEC076700D08578 /* kdtree.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = kdtree.h; sourceTree = "<group>"; };
		2D765BAF0FEC076700D08578 /* quickshift.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = quickshift.c; sourceTree = "<group>"; };
		2D765BB00FEC076700D08578 /* quickshift.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = quickshift.h; sourceTree = "<group>"; };
		2D7AD0800E38911C00783252 /* formatter.py */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 4; lastKnownFileType = text.script.python; name = formatter.py; path = docsrc/formatter.py; sourceTree = "<group>"; tabWidth = 4; };
		2D7AD0810E38911C00783252 /* mdoc.py */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 4; lastKnownFileType = text.script.python; name = mdoc.py; path = docsrc/mdoc.py; sourceTree = "<group>"; tabWidth = 4; };
		2D7AD0820E38911C00783252 /* webdoc.py */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 4; lastKnownFileType = text.script.python; name = webdoc.py; path = docsrc/webdoc.py; sourceTree = "<group>"; tabWidth = 4; };
		2D7AD0830E38911C00783252 /* wikidoc.py */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 4; lastKnownFileType = text.script.python; name = wikidoc.py; path = docsrc/wikidoc.py; sourceTree = "<group>"; tabWidth = 4; };
		2D7AD0A90E38C18600783252 /* aib.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = aib.html; path = docsrc/tutorials/aib.html; sourceTree = "<group>"; };
		2D7AD0AA0E38C18600783252 /* api.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = api.html; path = docsrc/api.html; sourceTree = "<group>"; };
		2D7AD0AB0E38C18600783252 /* download.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = download.html; path = docsrc/download.html; sourceTree = "<group>"; };
		2D7AD0AC0E38C18600783252 /* doxygen.conf */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; name = doxygen.conf; path = docsrc/doxygen.conf; sourceTree = "<group>"; };
		2D7AD0AD0E38C18600783252 /* hikm.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = hikm.html; path = docsrc/tutorials/hikm.html; sourceTree = "<group>"; };
		2D7AD0AE0E38C18600783252 /* ikm.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = ikm.html; path = docsrc/tutorials/ikm.html; sourceTree = "<group>"; };
		2D7AD0AF0E38C18600783252 /* index.html */ = {isa = PBXFileReference; explicitFileType = text.html.documentation; fileEncoding = 4; indentWidth = 2; name = index.html; path = docsrc/index.html; sourceTree = "<group>"; tabWidth = 2; };
		2D7AD0B00E38C18600783252 /* mser.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = mser.html; path = docsrc/tutorials/mser.html; sourceTree = "<group>"; };
		2D7AD0B10E38C18600783252 /* sift.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = sift.html; path = docsrc/tutorials/sift.html; sourceTree = "<group>"; };
		2D7AD0B30E38C18600783252 /* tutorials.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = tutorials.html; path = docsrc/tutorials.html; sourceTree = "<group>"; };
		2D7AD0B40E38C18600783252 /* utils.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = utils.html; path = docsrc/tutorials/utils.html; sourceTree = "<group>"; };
		2D81B9A617351EE5000706C0 /* vl_pegasos.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_pegasos.m; path = toolbox/misc/vl_pegasos.m; sourceTree = "<group>"; };
		2D81B9A717351EE5000706C0 /* vl_svmpegasos.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_svmpegasos.m; path = toolbox/misc/vl_svmpegasos.m; sourceTree = "<group>"; };
		2D81B9A917351EE5000706C0 /* vl_svmtrain.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_svmtrain.c; path = toolbox/misc/vl_svmtrain.c; sourceTree = "<group>"; };
		2D81B9AA17351EE5000706C0 /* vl_svmtrain.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_svmtrain.m; path = toolbox/misc/vl_svmtrain.m; sourceTree = "<group>"; };
		2D81B9AB17351EE5000706C0 /* vl_version.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_version.c; path = toolbox/misc/vl_version.c; sourceTree = "<group>"; };
		2D81B9B61735666E000706C0 /* vl_svmtrain.mexmaci64 */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.dylib"; includeInIndex = 0; path = vl_svmtrain.mexmaci64; sourceTree = BUILT_PRODUCTS_DIR; };
		2D85DE1314CD78AC00BDAE4E /* hog.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = hog.h; sourceTree = "<group>"; };
		2D85DE1414CD78BB00BDAE4E /* hog.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = hog.c; sourceTree = "<group>"; };
		2D86B1090F24CC9B00E625D6 /* vl_aibhist.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_aibhist.c; path = toolbox/aib/vl_aibhist.c; sourceTree = "<group>"; };
		2D86B10A0F24CC9B00E625D6 /* vl_alldist2.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_alldist2.c; path = toolbox/misc/vl_alldist2.c; sourceTree = "<group>"; };
		2D86B10C0F24CC9B00E625D6 /* vl_binsum.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_binsum.c; path = toolbox/misc/vl_binsum.c; sourceTree = "<group>"; };
		2D86B10E0F24CC9B00E625D6 /* vl_erfill.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_erfill.c; path = toolbox/mser/vl_erfill.c; sourceTree = "<group>"; };
		2D86B10F0F24CC9B00E625D6 /* vl_getpid.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_getpid.c; path = toolbox/misc/vl_getpid.c; sourceTree = "<group>"; };
		2D86B1100F24CC9B00E625D6 /* vl_hikmeans.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_hikmeans.c; path = toolbox/kmeans/vl_hikmeans.c; sourceTree = "<group>"; };
		2D86B1150F24CC9B00E625D6 /* vl_ikmeanspush.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_ikmeanspush.c; path = toolbox/kmeans/vl_ikmeanspush.c; sourceTree = "<group>"; };
		2D86B1180F24CC9B00E625D6 /* vl_imwbackwardmx.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_imwbackwardmx.c; path = toolbox/imop/vl_imwbackwardmx.c; sourceTree = "<group>"; };
		2D86B1190F24CC9B00E625D6 /* vl_inthist.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_inthist.c; path = toolbox/misc/vl_inthist.c; sourceTree = "<group>"; };
		2D86B11A0F24CC9B00E625D6 /* vl_irodr.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_irodr.c; path = toolbox/geometry/vl_irodr.c; sourceTree = "<group>"; };
		2D86B11B0F24CC9B00E625D6 /* vl_localmax.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_localmax.c; path = toolbox/misc/vl_localmax.c; sourceTree = "<group>"; };
		2D86B11D0F24CC9B00E625D6 /* vl_rodr.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_rodr.c; path = toolbox/geometry/vl_rodr.c; sourceTree = "<group>"; };
		2D86B11E0F24CC9B00E625D6 /* vl_sampleinthist.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_sampleinthist.c; path = toolbox/misc/vl_sampleinthist.c; sourceTree = "<group>"; };
		2D86B1200F24CC9B00E625D6 /* vl_siftdescriptor.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_siftdescriptor.c; path = toolbox/sift/vl_siftdescriptor.c; sourceTree = "<group>"; };
		2D86B1210F24CC9B00E625D6 /* vl_tpsumx.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_tpsumx.c; path = toolbox/imop/vl_tpsumx.c; sourceTree = "<group>"; };
		2D86B1230F24CC9B00E625D6 /* vl_ubcmatch.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_ubcmatch.c; path = toolbox/sift/vl_ubcmatch.c; sourceTree = "<group>"; };
		2D8E1B551371FB23009CDE11 /* .gitattributes */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = .gitattributes; sourceTree = "<group>"; };
		2D8E1B561371FB23009CDE11 /* .gitignore */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = .gitignore; sourceTree = "<group>"; };
		2D92043C13CDDD860004DC40 /* README.md */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = README.md; sourceTree = "<group>"; };
		2D94E6BC148E48440089ADA5 /* slic.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = slic.c; sourceTree = "<group>"; };
		2D94E6BE1490E4120089ADA5 /* slic.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = slic.h; sourceTree = "<group>"; };
		2D9941F410ECD04F00502DF6 /* heap-def.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "heap-def.h"; sourceTree = "<group>"; };
		2D99420410ECD6E300502DF6 /* index.html */ = {isa = PBXFileReference; explicitFileType = text.html.documentation; fileEncoding = 4; name = index.html; path = doc/index.html; sourceTree = "<group>"; };
		2D9A805D17745CB500B9A9CD /* vl_liop.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_liop.c; path = toolbox/sift/vl_liop.c; sourceTree = "<group>"; };
		2D9A805E17745CB500B9A9CD /* vl_liop.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_liop.m; path = toolbox/sift/vl_liop.m; sourceTree = "<group>"; };
		2D9A805F17745CB500B9A9CD /* vl_plotss.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_plotss.m; path = toolbox/sift/vl_plotss.m; sourceTree = "<group>"; };
		2D9A8060177476E300B9A9CD /* vl_demo_gmm_2d_rand.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_demo_gmm_2d_rand.m; path = toolbox/demo/vl_demo_gmm_2d_rand.m; sourceTree = "<group>"; };
		2D9A8061177476E300B9A9CD /* vl_demo_gmm_convergence.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_demo_gmm_convergence.m; path = toolbox/demo/vl_demo_gmm_convergence.m; sourceTree = "<group>"; };
		2D9A8062177476E300B9A9CD /* vl_demo_kmeans_2d.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_demo_kmeans_2d.m; path = toolbox/demo/vl_demo_kmeans_2d.m; sourceTree = "<group>"; };
		2D9A8063177476E300B9A9CD /* vl_test_gmm.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_test_gmm.m; path = toolbox/xtest/vl_test_gmm.m; sourceTree = "<group>"; };
		2D9A8064177476E300B9A9CD /* vl_vlad.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_vlad.m; path = toolbox/vlad/vl_vlad.m; sourceTree = "<group>"; };
		2D9A8065177476E300B9A9CD /* vl_plotbox.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_plotbox.m; path = toolbox/plotop/vl_plotbox.m; sourceTree = "<group>"; };
		2D9A8066177476E300B9A9CD /* vl_test_plotbox.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_test_plotbox.m; path = toolbox/xtest/vl_test_plotbox.m; sourceTree = "<group>"; };
		2D9A8067177476E300B9A9CD /* vl_impattern.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_impattern.m; path = toolbox/imop/vl_impattern.m; sourceTree = "<group>"; };
		2D9AAB2E1769B6A300AFBCFC /* test_gmm.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = test_gmm.c; sourceTree = "<group>"; };
		2D9AAB2F1769B6A300AFBCFC /* test_mathop_fast_resqrt.tc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = test_mathop_fast_resqrt.tc; sourceTree = "<group>"; };
		2D9AAB301769B6A300AFBCFC /* test_mathop_fast_sqrt_ui.tc */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = test_mathop_fast_sqrt_ui.tc; sourceTree = "<group>"; };
		2D9AAB311769B6A300AFBCFC /* test_mathop.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = test_mathop.c; sourceTree = "<group>"; };
		2D9AAB321769B6A300AFBCFC /* test_rand.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = test_rand.c; sourceTree = "<group>"; };
		2D9AAB331769B6A300AFBCFC /* test_svd2.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = test_svd2.c; sourceTree = "<group>"; };
		2DA21722141D48DA0047E7C7 /* vl_alphanum.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_alphanum.m; path = toolbox/misc/vl_alphanum.m; sourceTree = "<group>"; };
		2DA21723141D49350047E7C7 /* vl_binsum.def */ = {isa = PBXFileReference; explicitFileType = sourcecode.c.h; fileEncoding = 4; name = vl_binsum.def; path = toolbox/misc/vl_binsum.def; sourceTree = "<group>"; xcLanguageSpecificationIdentifier = xcode.lang.c; };
		2DA47BD91792E6C700EC02E0 /* vl_liop.mexmaci64 */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.dylib"; includeInIndex = 0; path = vl_liop.mexmaci64; sourceTree = BUILT_PRODUCTS_DIR; };
		2DA64CD217329C2400276F3D /* fisher.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = fisher.c; sourceTree = "<group>"; };
		2DA64CD317329C2400276F3D /* fisher.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = fisher.h; sourceTree = "<group>"; };
		2DA64CD417329C2400276F3D /* gmm.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = gmm.c; sourceTree = "<group>"; };
		2DA64CD517329C2400276F3D /* gmm.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = gmm.h; sourceTree = "<group>"; };
		2DA64CD617329C2400276F3D /* svm.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = svm.h; sourceTree = "<group>"; };
		2DA64CD717329C2400276F3D /* vlad.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = vlad.c; sourceTree = "<group>"; };
		2DA64CD817329C2400276F3D /* vlad.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = vlad.h; sourceTree = "<group>"; };
		2DA64CE01733089000276F3D /* svm.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = svm.c; sourceTree = "<group>"; };
		2DAB42AB19BB062700735B50 /* vl_demo_frame.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_demo_frame.m; path = toolbox/demo/vl_demo_frame.m; sourceTree = "<group>"; };
		2DAB42AD19BB062700735B50 /* vl_demo_hog.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_demo_hog.m; path = toolbox/demo/vl_demo_hog.m; sourceTree = "<group>"; };
		2DAB42AE19BB066800735B50 /* vl_isoctave.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_isoctave.m; path = toolbox/misc/vl_isoctave.m; sourceTree = "<group>"; };
		2DAB42AF19BB066800735B50 /* vl_matlabversion.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = vl_matlabversion.m; path = toolbox/misc/vl_matlabversion.m; sourceTree = "<group>"; };
		2DAF7CE3139918EA00FA0D07 /* about.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = about.html; path = docsrc/about.html; sourceTree = "<group>"; };
		2DAF7CE4139918EA00FA0D07 /* apps.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = apps.html; path = docsrc/apps.html; sourceTree = "<group>"; };
		2DAF7CE5139918EA00FA0D07 /* compiling.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = compiling.html; path = docsrc/compiling.html; sourceTree = "<group>"; };
		2DAF7CE6139918EA00FA0D07 /* doc.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = doc.html; path = docsrc/doc.html; sourceTree = "<group>"; };
		2DAF7CE7139918EA00FA0D07 /* dsift.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = dsift.html; path = docsrc/tutorials/dsift.html; sourceTree = "<group>"; };
		2DAF7CE8139918EA00FA0D07 /* install-c.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = "install-c.html"; path = "docsrc/install-c.html"; sourceTree = "<group>"; };
		2DAF7CE9139918EA00FA0D07 /* install-matlab.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = "install-matlab.html"; path = "docsrc/install-matlab.html"; sourceTree = "<group>"; };
		2DAF7CEA139918EA00FA0D07 /* install-shell.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = "install-shell.html"; path = "docsrc/install-shell.html"; sourceTree = "<group>"; };
		2DAF7CEC139918EA00FA0D07 /* pygmentize.css */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.css; name = pygmentize.css; path = docsrc/pygmentize.css; sourceTree = "<group>"; };
		2DAF7CED139918EA00FA0D07 /* quickshift.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = quickshift.html; path = docsrc/tutorials/quickshift.html; sourceTree = "<group>"; };
		2DAF7CEE139918EA00FA0D07 /* using-gcc.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = "using-gcc.html"; path = "docsrc/using-gcc.html"; sourceTree = "<group>"; };
		2DAF7CEF139918EA00FA0D07 /* using-vsexpress.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = "using-vsexpress.html"; path = "docsrc/using-vsexpress.html"; sourceTree = "<group>"; };
		2DAF7CF0139918EA00FA0D07 /* using-xcode.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = "using-xcode.html"; path = "docsrc/using-xcode.html"; sourceTree = "<group>"; };
		2DAF7CF21399190700FA0D07 /* kdtree.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = kdtree.html; path = docsrc/tutorials/kdtree.html; sourceTree = "<group>"; };
		2DB1A28F110CE32F00E02BD8 /* inthist.tc */ = {isa = PBXFileReference; explicitFileType = sourcecode.c.c; fileEncoding = 4; name = inthist.tc; path = toolbox/misc/inthist.tc; sourceTree = "<group>"; };
		2DB1A290110CE32F00E02BD8 /* samplinthist.tc */ = {isa = PBXFileReference; explicitFileType = sourcecode.c.c; fileEncoding = 4; name = samplinthist.tc; path = toolbox/misc/samplinthist.tc; sourceTree = "<group>"; };
		2DB1A2A1110D09B200E02BD8 /* vl_quickshift.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_quickshift.c; path = toolbox/quickshift/vl_quickshift.c; sourceTree = "<group>"; };
		2DB3F04F1605DFDB00862CCA /* vl_covdet.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_covdet.c; path = toolbox/sift/vl_covdet.c; sourceTree = "<group>"; };
		2DB3F0501605DFDB00862CCA /* vl_covdet.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_covdet.m; path = toolbox/sift/vl_covdet.m; sourceTree = "<group>"; };
		2DB3F0591605EC0B00862CCA /* vl_covdet.mexmaci64 */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.dylib"; includeInIndex = 0; path = vl_covdet.mexmaci64; sourceTree = BUILT_PRODUCTS_DIR; };
		2DB6ABF214CDB7B500A27D16 /* vl_hog.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_hog.c; path = toolbox/misc/vl_hog.c; sourceTree = "<group>"; };
		2DBD0AA110F78FEB004DBA31 /* qsort-def.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "qsort-def.h"; sourceTree = "<group>"; };
		2DBD0AA410F7A48C004DBA31 /* test_qsort-def.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "test_qsort-def.c"; sourceTree = "<group>"; };
		2DC75D0B0E4B4FE7005223E7 /* Makefile.mak */ = {isa = PBXFileReference; explicitFileType = sourcecode.make; fileEncoding = 4; indentWidth = 8; path = Makefile.mak; sourceTree = "<group>"; tabWidth = 8; usesTabs = 1; };
		2DD302780DE33107009443C7 /* random.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = random.c; sourceTree = "<group>"; };
		2DD302790DE33107009443C7 /* random.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = random.h; sourceTree = "<group>"; };
		2DD99CC40E58A86B00CE1DA1 /* imopv_sse2.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = imopv_sse2.c; sourceTree = "<group>"; };
		2DD99CC50E58A8C700CE1DA1 /* imopv_sse2.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = imopv_sse2.h; sourceTree = "<group>"; };
		2DD99E8F0E59EA8E00CE1DA1 /* host.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = host.h; sourceTree = "<group>"; };
		2DD99E900E59EA8E00CE1DA1 /* host.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = host.c; sourceTree = "<group>"; };
		2DD9A01F0E5A296700CE1DA1 /* test_host.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = test_host.c; sourceTree = "<group>"; };
		2DD9AD1E17F9C41200C1FC78 /* vl_kmeans.mexmaci64 */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.dylib"; includeInIndex = 0; path = vl_kmeans.mexmaci64; sourceTree = BUILT_PRODUCTS_DIR; };
		2DDA2306124BD104003F6A9D /* lbp.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = lbp.h; sourceTree = "<group>"; };
		2DDA2307124BD104003F6A9D /* lbp.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = lbp.c; sourceTree = "<group>"; };
		2DE5B37D0FDC2BE9008CEB1D /* dsift.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = dsift.c; sourceTree = "<group>"; };
		2DE5B37E0FDC2BE9008CEB1D /* dsift.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = dsift.h; sourceTree = "<group>"; };
		2DE5B3A80FDC3471008CEB1D /* vl_dsift.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_dsift.c; path = toolbox/sift/vl_dsift.c; sourceTree = "<group>"; };
		2DE607661785722900E1A24E /* vl_vlad.mexmaci64 */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.dylib"; includeInIndex = 0; path = vl_vlad.mexmaci64; sourceTree = BUILT_PRODUCTS_DIR; };
		2DE765220FF23EE9006347E0 /* kdtree.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = kdtree.h; path = toolbox/misc/kdtree.h; sourceTree = "<group>"; };
		2DF4565610ADB9010083F316 /* vl_homkermap.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_homkermap.c; path = toolbox/misc/vl_homkermap.c; sourceTree = "<group>"; };
		2DFA239E173B7D0F0065603E /* vl_gmm.mexmaci64 */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.dylib"; includeInIndex = 0; path = vl_gmm.mexmaci64; sourceTree = BUILT_PRODUCTS_DIR; };
		2DFA239F173B7DFB0065603E /* vl_gmm.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_gmm.c; path = toolbox/gmm/vl_gmm.c; sourceTree = "<group>"; };
		2DFA23A0173B7DFB0065603E /* vl_gmm.m */ = {isa = PBXFileReference; explicitFileType = text; fileEncoding = 4; name = vl_gmm.m; path = toolbox/gmm/vl_gmm.m; sourceTree = "<group>"; };
		2DFA23A2173B7F1C0065603E /* liop.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = liop.c; sourceTree = "<group>"; };
		2DFA23A3173B7F1C0065603E /* liop.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = liop.h; sourceTree = "<group>"; };
		2DFA23A6173ECE7C0065603E /* vl_threads.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; name = vl_threads.c; path = toolbox/misc/vl_threads.c; sourceTree = "<group>"; };
		2DFA36D212F1A26D00E808D9 /* array.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = array.c; sourceTree = "<group>"; };
		2DFA36D312F1A26D00E808D9 /* array.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = array.h; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		2D0E8A7B1786D18D005419DC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2D0E8A7C1786D18D005419DC /* libvl.dylib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D1EECCF1603E85200C63DCE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D81B9B01735666E000706C0 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2D81B9B11735666E000706C0 /* libvl.dylib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2DA47BD31792E6C700EC02E0 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2DA47BD41792E6C700EC02E0 /* libvl.dylib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2DB3F0561605EC0B00862CCA /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2DB3F06B1605F3A100862CCA /* libvl.dylib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2DD9AD1817F9C41200C1FC78 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2DD9AD1917F9C41200C1FC78 /* libvl.dylib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2DE607601785722900E1A24E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2DE607611785722900E1A24E /* libvl.dylib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2DFA2398173B7D0F0065603E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2DFA2399173B7D0F0065603E /* libvl.dylib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		08FB7794FE84155DC02AAC07 /* vlfeat */ = {
			isa = PBXGroup;
			children = (
				2D8E1B551371FB23009CDE11 /* .gitattributes */,
				2D8E1B561371FB23009CDE11 /* .gitignore */,
				2D7AD0870E38C15700783252 /* doc */,
				2D6102CF111EE22C00470F4C /* make */,
				2D732DE30CF8C2CB0099B03C /* Makefile */,
				2DC75D0B0E4B4FE7005223E7 /* Makefile.mak */,
				2D92043C13CDDD860004DC40 /* README.md */,
				2D732DE40CF8C2E40099B03C /* src */,
				2D732EAF0CF8C3950099B03C /* toolbox */,
				2D66AAAE13AE03B20060594E /* toolbox-mfiles */,
				2D732E7D0CF8C2E40099B03C /* vl */,
				2D81B9B61735666E000706C0 /* vl_svmtrain.mexmaci64 */,
				2DB3F0591605EC0B00862CCA /* vl_covdet.mexmaci64 */,
				2DFA239E173B7D0F0065603E /* vl_gmm.mexmaci64 */,
				2DE607661785722900E1A24E /* vl_vlad.mexmaci64 */,
				2D0E8A811786D18D005419DC /* vl_fisher.mexmaci64 */,
				2DA47BD91792E6C700EC02E0 /* vl_liop.mexmaci64 */,
				2DD9AD1E17F9C41200C1FC78 /* vl_kmeans.mexmaci64 */,
				2D1EECD21603E85200C63DCE /* libvl.dylib */,
			);
			indentWidth = 2;
			name = vlfeat;
			sourceTree = "<group>";
			tabWidth = 2;
		};
		2D09A29813AE081700C79C6A /* demo */ = {
			isa = PBXGroup;
			children = (
				2D66AC8913AE05BD0060594E /* vl_demo.m */,
				2D66AC7013AE05BD0060594E /* vl_demo_aib.m */,
				2D66AC7113AE05BD0060594E /* vl_demo_alldist.m */,
				2D66AC7213AE05BD0060594E /* vl_demo_cmd.m */,
				2D141E30178376CA00E2958B /* vl_demo_covdet.m */,
				2D66AC7313AE05BD0060594E /* vl_demo_dsift.m */,
				2DAB42AB19BB062700735B50 /* vl_demo_frame.m */,
				2D9A8060177476E300B9A9CD /* vl_demo_gmm_2d_rand.m */,
				2D141E21178376CA00E2958B /* vl_demo_gmm_2d_twist.m */,
				2D141E1F178376CA00E2958B /* vl_demo_gmm_3d.m */,
				2D9A8061177476E300B9A9CD /* vl_demo_gmm_convergence.m */,
				2DAB42AD19BB062700735B50 /* vl_demo_hog.m */,
				2D4938B01895BAAA00B775EE /* vl_demo_ikmeans.m */,
				2D66AC7413AE05BD0060594E /* vl_demo_imdisttf.m */,
				2D66AC7A13AE05BD0060594E /* vl_demo_kdtree.m */,
				2D66AC7513AE05BD0060594E /* vl_demo_kdtree_ann.m */,
				2D66AC7613AE05BD0060594E /* vl_demo_kdtree_forest.m */,
				2D66AC7713AE05BD0060594E /* vl_demo_kdtree_plot.m */,
				2D66AC7813AE05BD0060594E /* vl_demo_kdtree_self.m */,
				2D66AC7913AE05BD0060594E /* vl_demo_kdtree_sift.m */,
				2D9A8062177476E300B9A9CD /* vl_demo_kmeans_2d.m */,
				2D141E22178376CA00E2958B /* vl_demo_kmeans_ann_speed.m */,
				2D66AC7B13AE05BD0060594E /* vl_demo_kmeans_init.m */,
				2D66AC7C13AE05BD0060594E /* vl_demo_kmeans_vs_builtin.m */,
				2D66AC7D13AE05BD0060594E /* vl_demo_mser_basic.m */,
				2D66AC7E13AE05BD0060594E /* vl_demo_mser_cmd.m */,
				2D66AC7F13AE05BD0060594E /* vl_demo_mser_delta.m */,
				2D141E2D178376CA00E2958B /* vl_demo_plots_rank.m */,
				2D66AC8013AE05BD0060594E /* vl_demo_print.m */,
				2D66AC8113AE05BD0060594E /* vl_demo_quickshift.m */,
				2D66AC8213AE05BD0060594E /* vl_demo_sift_basic.m */,
				2D66AC8313AE05BD0060594E /* vl_demo_sift_cmd.m */,
				2D66AC8413AE05BD0060594E /* vl_demo_sift_edge.m */,
				2D66AC8513AE05BD0060594E /* vl_demo_sift_match.m */,
				2D66AC8613AE05BD0060594E /* vl_demo_sift_or.m */,
				2D66AC8713AE05BD0060594E /* vl_demo_sift_peak.m */,
				2D66AC8813AE05BD0060594E /* vl_demo_sift_vs_ubc.m */,
				2D141E2E178376CA00E2958B /* vl_demo_slic.m */,
				2D141E23178376CA00E2958B /* vl_demo_svm.m */,
			);
			name = demo;
			sourceTree = "<group>";
		};
		2D09A29913AE084500C79C6A /* xtest */ = {
			isa = PBXGroup;
			children = (
				2D66AC6413AE05BD0060594E /* vl_assert_almost_equal.m */,
				2D66AC6513AE05BD0060594E /* vl_assert_equal.m */,
				2D66AC6613AE05BD0060594E /* vl_assert_exception.m */,
				2D66ACFD13AE05BD0060594E /* vl_test.m */,
				2D10471316D54C45001677AD /* vl_test_aib.m */,
				2D66ACD613AE05BD0060594E /* vl_test_alldist.m */,
				2D10471416D54C45001677AD /* vl_test_alldist2.m */,
				2D10471516D54C45001677AD /* vl_test_alphanum.m */,
				2D66ACD913AE05BD0060594E /* vl_test_argparse.m */,
				2D66ACDB13AE05BD0060594E /* vl_test_binsearch.m */,
				2D66ACDD13AE05BD0060594E /* vl_test_binsum.m */,
				2D10471616D54C45001677AD /* vl_test_colsubset.m */,
				2D10471716D54C45001677AD /* vl_test_cummax.m */,
				2D66ACE013AE05BD0060594E /* vl_test_dsift.m */,
				2D9A8063177476E300B9A9CD /* vl_test_gmm.m */,
				2D66ACE113AE05BD0060594E /* vl_test_grad.m */,
				2D10471816D54C45001677AD /* vl_test_hikmeans.m */,
				2D10471916D54C45001677AD /* vl_test_hog.m */,
				2D66ACE313AE05BD0060594E /* vl_test_homkermap.m */,
				2D10471A16D54C45001677AD /* vl_test_ihashsum.m */,
				2D10471B16D54C45001677AD /* vl_test_ikmeans.m */,
				2D66ACE713AE05BD0060594E /* vl_test_imarray.m */,
				2D66ACE813AE05BD0060594E /* vl_test_imdisttf.m */,
				2D66ACEA13AE05BD0060594E /* vl_test_imintegral.m */,
				2D66ACEC13AE05BD0060594E /* vl_test_imsmooth.m */,
				2D66ACED13AE05BD0060594E /* vl_test_imwbackward.m */,
				2D66ACEF13AE05BD0060594E /* vl_test_init.m */,
				2D10471C16D54C45001677AD /* vl_test_inthist.m */,
				2D66ACF113AE05BD0060594E /* vl_test_kdtree.m */,
				2D66ACF213AE05BD0060594E /* vl_test_kmeans.m */,
				2D66ACF313AE05BD0060594E /* vl_test_lbp.m */,
				2D10471D16D54C45001677AD /* vl_test_maketrainingset.m */,
				2D10471E16D54C45001677AD /* vl_test_mser.m */,
				2D66ACF713AE05BD0060594E /* vl_test_phow.m */,
				2D10471F16D54C45001677AD /* vl_test_pr.m */,
				2D141E29178376CA00E2958B /* vl_test_printsize.m */,
				2D10472016D54C45001677AD /* vl_test_roc.m */,
				2D66ACF913AE05BD0060594E /* vl_test_sift.m */,
				2D10472116D54C45001677AD /* vl_test_slic.m */,
				2D141E24178376CA00E2958B /* vl_test_svmtrain.m */,
				2D66ACFB13AE05BD0060594E /* vl_test_twister.m */,
				2D66ACFC13AE05BD0060594E /* vl_test_whistc.m */,
			);
			name = xtest;
			sourceTree = "<group>";
		};
		2D10470C16D546AC001677AD /* segmentation */ = {
			isa = PBXGroup;
			children = (
				2D66ACC713AE05BD0060594E /* vl_quickseg.m */,
				2D66ACC813AE05BD0060594E /* vl_quickshift.m */,
				2D66ACC913AE05BD0060594E /* vl_quickvis.m */,
				2D6E381A149CE4B5005319B7 /* vl_slic.m */,
			);
			name = segmentation;
			sourceTree = "<group>";
		};
		2D10470D16D546B8001677AD /* plotting and gui */ = {
			isa = PBXGroup;
			children = (
				2D141E33178376CA00E2958B /* vl_det.m */,
				2D141E31178376CA00E2958B /* vl_plotstyle.m */,
				2D66AC6913AE05BD0060594E /* vl_cf.m */,
				2D66AC6A13AE05BD0060594E /* vl_click.m */,
				2D66AC6B13AE05BD0060594E /* vl_clickpoint.m */,
				2D66AC6C13AE05BD0060594E /* vl_clicksegment.m */,
				2D66AC9013AE05BD0060594E /* vl_figaspect.m */,
				2D66ACB813AE05BD0060594E /* vl_linespec2prop.m */,
				2D66ACC113AE05BD0060594E /* vl_plotframe.m */,
				2D66ACC213AE05BD0060594E /* vl_plotgrid.m */,
				2D66ACC313AE05BD0060594E /* vl_plotpoint.m */,
				2D66ACC413AE05BD0060594E /* vl_plotsiftdescriptor.m */,
				2D66ACC513AE05BD0060594E /* vl_pr.m */,
				2D66ACC613AE05BD0060594E /* vl_printsize.m */,
				2D66ACCC13AE05BD0060594E /* vl_roc.m */,
				2D66ACFE13AE05BD0060594E /* vl_tightsubplot.m */,
			);
			name = "plotting and gui";
			sourceTree = "<group>";
		};
		2D10470E16D546CF001677AD /* image procesing */ = {
			isa = PBXGroup;
			children = (
				2D66ACA313AE05BD0060594E /* vl_imarray.m */,
				2D66ACA413AE05BD0060594E /* vl_imarraysc.m */,
				2D66ACA513AE05BD0060594E /* vl_imdisttf.m */,
				2D66ACA613AE05BD0060594E /* vl_imdown.m */,
				2D66ACA713AE05BD0060594E /* vl_imgrad.m */,
				2D66ACA813AE05BD0060594E /* vl_imintegral.m */,
				2D66ACA913AE05BD0060594E /* vl_imreadgray.m */,
				2D66ACAA13AE05BD0060594E /* vl_imsc.m */,
				2D66ACAB13AE05BD0060594E /* vl_imseg.m */,
				2D66ACAC13AE05BD0060594E /* vl_imsmooth.m */,
				2D66ACAE13AE05BD0060594E /* vl_imup.m */,
				2D66ACAF13AE05BD0060594E /* vl_imwbackward.m */,
				2D66ACB013AE05BD0060594E /* vl_imwhiten.m */,
				2D66ACB113AE05BD0060594E /* vl_inthist.m */,
				2D66ACCB13AE05BD0060594E /* vl_rgb2xyz.m */,
				2D66ACCF13AE05BD0060594E /* vl_sampleinthist.m */,
				2D66ACFF13AE05BD0060594E /* vl_tps.m */,
				2D66AD0013AE05BD0060594E /* vl_tpsu.m */,
				2D66AD0713AE05BD0060594E /* vl_waffine.m */,
				2D66AD0913AE05BD0060594E /* vl_witps.m */,
				2D66AD0A13AE05BD0060594E /* vl_wtps.m */,
				2D66AD0C13AE05BD0060594E /* vl_xyz2lab.m */,
				2D66AD0D13AE05BD0060594E /* vl_xyz2luv.m */,
				2D66AD0E13AE05BD0060594E /* vl_xyz2rgb.m */,
			);
			name = "image procesing";
			sourceTree = "<group>";
		};
		2D10470F16D546E3001677AD /* clustering and indexing */ = {
			isa = PBXGroup;
			children = (
				2DFA23A0173B7DFB0065603E /* vl_gmm.m */,
				2D66AC9813AE05BD0060594E /* vl_hikmeans.m */,
				2D66AC9913AE05BD0060594E /* vl_hikmeanshist.m */,
				2D66AC9A13AE05BD0060594E /* vl_hikmeanspush.m */,
				2D66AC9D13AE05BD0060594E /* vl_ihashfind.m */,
				2D66AC9E13AE05BD0060594E /* vl_ihashsum.m */,
				2D66ACA013AE05BD0060594E /* vl_ikmeans.m */,
				2D66ACA113AE05BD0060594E /* vl_ikmeanshist.m */,
				2D66ACA213AE05BD0060594E /* vl_ikmeanspush.m */,
				2D66ACB313AE05BD0060594E /* vl_kdtreebuild.m */,
				2D66ACB413AE05BD0060594E /* vl_kdtreequery.m */,
				2D66ACB513AE05BD0060594E /* vl_kmeans.m */,
			);
			name = "clustering and indexing";
			sourceTree = "<group>";
		};
		2D10471016D54711001677AD /* features */ = {
			isa = PBXGroup;
			children = (
				2DB3F0501605DFDB00862CCA /* vl_covdet.m */,
				2D66AC8B13AE05BD0060594E /* vl_dsift.m */,
				2D66AC8E13AE05BD0060594E /* vl_erfill.m */,
				2D66AC8F13AE05BD0060594E /* vl_ertr.m */,
				2D141E25178376CA00E2958B /* vl_fisher.m */,
				2D141E26178376CA00E2958B /* vl_frame2oell.m */,
				2D66AC9513AE05BD0060594E /* vl_harris.m */,
				2D3E1F2F15FFE6110035AA1E /* vl_hog.m */,
				2D9A8067177476E300B9A9CD /* vl_impattern.m */,
				2D66ACB613AE05BD0060594E /* vl_lbp.m */,
				2D66ACB713AE05BD0060594E /* vl_lbpfliplr.m */,
				2D9A805E17745CB500B9A9CD /* vl_liop.m */,
				2D66ACBA13AE05BD0060594E /* vl_mser.m */,
				2D66ACC013AE05BD0060594E /* vl_phow.m */,
				2D9A8065177476E300B9A9CD /* vl_plotbox.m */,
				2D9A805F17745CB500B9A9CD /* vl_plotss.m */,
				2D66ACD113AE05BD0060594E /* vl_sift.m */,
				2D66ACD213AE05BD0060594E /* vl_siftdescriptor.m */,
				2D9A8066177476E300B9A9CD /* vl_test_plotbox.m */,
				2D66AD0413AE05BD0060594E /* vl_ubcmatch.m */,
				2D66AD0513AE05BD0060594E /* vl_ubcread.m */,
				2D9A8064177476E300B9A9CD /* vl_vlad.m */,
			);
			name = features;
			sourceTree = "<group>";
		};
		2D10471116D54831001677AD /* utilities */ = {
			isa = PBXGroup;
			children = (
				2DA21722141D48DA0047E7C7 /* vl_alphanum.m */,
				2D66AC6313AE05BD0060594E /* vl_argparse.m */,
				2D66AC6713AE05BD0060594E /* vl_binsearch.m */,
				2D66AC6813AE05BD0060594E /* vl_binsum.m */,
				2D66AC6D13AE05BD0060594E /* vl_colsubset.m */,
				2D141E2C178376CA00E2958B /* vl_cummax.m */,
				2D66AC6F13AE05BD0060594E /* vl_ddgaussian.m */,
				2D66AC8A13AE05BD0060594E /* vl_dgaussian.m */,
				2D66AC8C13AE05BD0060594E /* vl_dsigmoid.m */,
				2D66AC8D13AE05BD0060594E /* vl_dwaffine.m */,
				2D66AC9213AE05BD0060594E /* vl_gaussian.m */,
				2D66AC9313AE05BD0060594E /* vl_getpid.m */,
				2D66AC9413AE05BD0060594E /* vl_grad.m */,
				2D66AC9613AE05BD0060594E /* vl_hat.m */,
				2D66AC9B13AE05BD0060594E /* vl_histmarg.m */,
				2D66AC9F13AE05BD0060594E /* vl_ihat.m */,
				2D141E2F178376CA00E2958B /* vl_imreadbw.m */,
				2D66ACB213AE05BD0060594E /* vl_irodr.m */,
				2DAB42AE19BB066800735B50 /* vl_isoctave.m */,
				2D66ACB913AE05BD0060594E /* vl_localmax.m */,
				2DAB42AF19BB066800735B50 /* vl_matlabversion.m */,
				2D66ACBC13AE05BD0060594E /* vl_numder.m */,
				2D66ACBD13AE05BD0060594E /* vl_numder2.m */,
				2D66ACBE13AE05BD0060594E /* vl_override.m */,
				2D66ACCA13AE05BD0060594E /* vl_rcos.m */,
				2D66ACCD13AE05BD0060594E /* vl_rodr.m */,
				2D66ACD313AE05BD0060594E /* vl_sigmoid.m */,
				2D141E27178376CA00E2958B /* vl_threads.m */,
				2D66AD0313AE05BD0060594E /* vl_twister.m */,
				2D66AD0813AE05BD0060594E /* vl_whistc.m */,
				2D66AD0B13AE05BD0060594E /* vl_xmkdir.m */,
			);
			name = utilities;
			sourceTree = "<group>";
		};
		2D10471216D54938001677AD /* utilities */ = {
			isa = PBXGroup;
			children = (
				2DB1A28F110CE32F00E02BD8 /* inthist.tc */,
				2DB1A290110CE32F00E02BD8 /* samplinthist.tc */,
				2D1EECFE16046F1C00C63DCE /* vl_alldist.c */,
				2D86B10A0F24CC9B00E625D6 /* vl_alldist2.c */,
				2D1EED0016046F1C00C63DCE /* vl_binsearch.c */,
				2D86B10C0F24CC9B00E625D6 /* vl_binsum.c */,
				2DA21723141D49350047E7C7 /* vl_binsum.def */,
				2D1EED0216046F1C00C63DCE /* vl_cummax.c */,
				2D86B10F0F24CC9B00E625D6 /* vl_getpid.c */,
				2D86B1190F24CC9B00E625D6 /* vl_inthist.c */,
				2D86B11A0F24CC9B00E625D6 /* vl_irodr.c */,
				2D86B11B0F24CC9B00E625D6 /* vl_localmax.c */,
				2D1EED0516046F1C00C63DCE /* vl_getpid.c */,
				2D86B11D0F24CC9B00E625D6 /* vl_rodr.c */,
				2D86B11E0F24CC9B00E625D6 /* vl_sampleinthist.c */,
				2D86B1210F24CC9B00E625D6 /* vl_tpsumx.c */,
				2D1EED1A16046F1C00C63DCE /* vl_twister.c */,
				2D13F028100A6E8800C072E8 /* vl_alldist.c */,
			);
			name = utilities;
			sourceTree = "<group>";
		};
		2D21909118709C190094BCA2 /* tutorials */ = {
			isa = PBXGroup;
			children = (
				2D7AD0A90E38C18600783252 /* aib.html */,
				2D21907E187099540094BCA2 /* covdet.html */,
				2DAF7CE7139918EA00FA0D07 /* dsift.html */,
				2D219093187578140094BCA2 /* encode.html */,
				2D21909218709C590094BCA2 /* frame.html */,
				2D219080187099540094BCA2 /* gmm.html */,
				2D7AD0AD0E38C18600783252 /* hikm.html */,
				2D219081187099540094BCA2 /* hog.html */,
				2D7AD0AE0E38C18600783252 /* ikm.html */,
				2D219094187578140094BCA2 /* imdisttf.html */,
				2DAF7CF21399190700FA0D07 /* kdtree.html */,
				2D219083187099540094BCA2 /* kmeans.html */,
				2D219084187099540094BCA2 /* liop.html */,
				2D7AD0B00E38C18600783252 /* mser.html */,
				2D3E1F2D15FE93480035AA1E /* plots-rank.html */,
				2DAF7CED139918EA00FA0D07 /* quickshift.html */,
				2D7AD0B10E38C18600783252 /* sift.html */,
				2D6E381B149CE4ED005319B7 /* slic.html */,
				2D219087187099550094BCA2 /* svm.html */,
				2D7AD0B40E38C18600783252 /* utils.html */,
			);
			name = tutorials;
			sourceTree = "<group>";
		};
		2D28A00116D540D100E81844 /* statistical methods */ = {
			isa = PBXGroup;
			children = (
				2D1EECCC1603E1B100C63DCE /* svmdataset.c */,
				2D1EECCD1603E1B100C63DCE /* svmdataset.h */,
				2D519080115A800C0079E222 /* homkermap.h */,
				2D519081115A800C0079E222 /* homkermap.c */,
				2D732E7E0CF8C2E40099B03C /* aib.c */,
				2D732E7F0CF8C2E40099B03C /* aib.h */,
				2DA64CD617329C2400276F3D /* svm.h */,
				2DA64CE01733089000276F3D /* svm.c */,
			);
			name = "statistical methods";
			sourceTree = "<group>";
		};
		2D28A00216D540DD00E81844 /* statistical methods */ = {
			isa = PBXGroup;
			children = (
				2D289FFB16D53FC200E81844 /* svms_common.h */,
				2D1EECF716046F1B00C63DCE /* vl_aib.c */,
				2D86B1090F24CC9B00E625D6 /* vl_aibhist.c */,
				2DF4565610ADB9010083F316 /* vl_homkermap.c */,
				2D1EECF116046F1B00C63DCE /* vl_maketrainingset.c */,
				2D81B9A917351EE5000706C0 /* vl_svmtrain.c */,
				2D81B9AB17351EE5000706C0 /* vl_version.c */,
			);
			name = "statistical methods";
			sourceTree = "<group>";
		};
		2D28A00316D5420800E81844 /* statistical methods */ = {
			isa = PBXGroup;
			children = (
				2D66AC5D13AE05BD0060594E /* vl_aib.m */,
				2D66AC5E13AE05BD0060594E /* vl_aibcut.m */,
				2D66AC5F13AE05BD0060594E /* vl_aibcuthist.m */,
				2D66AC6013AE05BD0060594E /* vl_aibcutpush.m */,
				2D66AC6113AE05BD0060594E /* vl_aibhist.m */,
				2D66AC6213AE05BD0060594E /* vl_alldist2.m */,
				2D66AC9113AE05BD0060594E /* vl_flatmap.m */,
				2D66AC9C13AE05BD0060594E /* vl_homkermap.m */,
				2D289FFC16D53FC200E81844 /* vl_maketrainingset.m */,
				2D81B9A617351EE5000706C0 /* vl_pegasos.m */,
				2D141E28178376CA00E2958B /* vl_svmdataset.m */,
				2D81B9A717351EE5000706C0 /* vl_svmpegasos.m */,
				2D81B9AA17351EE5000706C0 /* vl_svmtrain.m */,
			);
			name = "statistical methods";
			sourceTree = "<group>";
		};
		2D28A00416D5422500E81844 /* features */ = {
			isa = PBXGroup;
			children = (
				2DB3F04F1605DFDB00862CCA /* vl_covdet.c */,
				2D1EED0316046F1C00C63DCE /* vl_dsift.c */,
				2D86B10E0F24CC9B00E625D6 /* vl_erfill.c */,
				2D141E20178376CA00E2958B /* vl_fisher.c */,
				2DB6ABF214CDB7B500A27D16 /* vl_hog.c */,
				2D28A00816D5435E00E81844 /* vl_lbp.c */,
				2D9A805D17745CB500B9A9CD /* vl_liop.c */,
				2D1EECFB16046F1C00C63DCE /* vl_sift.c */,
				2D86B1200F24CC9B00E625D6 /* vl_siftdescriptor.c */,
				2D86B1230F24CC9B00E625D6 /* vl_ubcmatch.c */,
				2D141E2B178376CA00E2958B /* vl_vlad.c */,
				2DE5B3A80FDC3471008CEB1D /* vl_dsift.c */,
			);
			name = features;
			sourceTree = "<group>";
		};
		2D28A00516D5425B00E81844 /* clustering and indexing */ = {
			isa = PBXGroup;
			children = (
				2DE765220FF23EE9006347E0 /* kdtree.h */,
				2DFA239F173B7DFB0065603E /* vl_gmm.c */,
				2D86B1100F24CC9B00E625D6 /* vl_hikmeans.c */,
				2D1EED0716046F1C00C63DCE /* vl_hikmeanspush.c */,
				2D1EECF916046F1B00C63DCE /* vl_ihashfind.c */,
				2D1EECF416046F1B00C63DCE /* vl_ihashsum.c */,
				2D141E32178376CA00E2958B /* vl_ikmeans.c */,
				2D86B1150F24CC9B00E625D6 /* vl_ikmeanspush.c */,
				2D1EED1116046F1C00C63DCE /* vl_kdtreebuild.c */,
				2D1EED1216046F1C00C63DCE /* vl_kdtreequery.c */,
				2D1EECFA16046F1B00C63DCE /* vl_kmeans.c */,
				2D1EED0616046F1C00C63DCE /* vl_hikmeans.c */,
			);
			name = "clustering and indexing";
			sourceTree = "<group>";
		};
		2D28A00616D5429C00E81844 /* segmentation */ = {
			isa = PBXGroup;
			children = (
				2DB1A2A1110D09B200E02BD8 /* vl_quickshift.c */,
				2D3F063417838CF100B27808 /* vl_slic.c */,
			);
			name = segmentation;
			sourceTree = "<group>";
		};
		2D28A00716D542C200E81844 /* image processing */ = {
			isa = PBXGroup;
			children = (
				2D1EED0B16046F1C00C63DCE /* vl_imdisttf.c */,
				2D1EED0C16046F1C00C63DCE /* vl_imintegral.c */,
				2D1EED0D16046F1C00C63DCE /* vl_imsmooth.c */,
				2D86B1180F24CC9B00E625D6 /* vl_imwbackwardmx.c */,
				2D1EED1916046F1C00C63DCE /* vl_tpsumx.c */,
				2D27614810FF6B3900D14D9E /* vl_imdisttf.c */,
			);
			name = "image processing";
			sourceTree = "<group>";
		};
		2D6102CF111EE22C00470F4C /* make */ = {
			isa = PBXGroup;
			children = (
				2D6102D0111EE22C00470F4C /* bin.mak */,
				2D6102D1111EE22C00470F4C /* dist.mak */,
				2D6102D2111EE22C00470F4C /* dll.mak */,
				2D6102D3111EE22C00470F4C /* doc.mak */,
				2D6102D4111EE22C00470F4C /* matlab.mak */,
				2D6102D5111EE22C00470F4C /* octave.mak */,
			);
			path = make;
			sourceTree = "<group>";
		};
		2D66AAAE13AE03B20060594E /* toolbox-mfiles */ = {
			isa = PBXGroup;
			children = (
				2D66AC6E13AE05BD0060594E /* vl_compile.m */,
				2D66AC9713AE05BD0060594E /* vl_help.m */,
				2D66ACBB13AE05BD0060594E /* vl_noprefix.m */,
				2D66ACCE13AE05BD0060594E /* vl_root.m */,
				2D66ACD013AE05BD0060594E /* vl_setup.m */,
				2D66ACD413AE05BD0060594E /* vl_simdctrl.m */,
				2D66AD0613AE05BD0060594E /* vl_version.m */,
				2D10470F16D546E3001677AD /* clustering and indexing */,
				2D09A29813AE081700C79C6A /* demo */,
				2D10471016D54711001677AD /* features */,
				2D10470E16D546CF001677AD /* image procesing */,
				2D10470D16D546B8001677AD /* plotting and gui */,
				2D10470C16D546AC001677AD /* segmentation */,
				2D28A00316D5420800E81844 /* statistical methods */,
				2D10471116D54831001677AD /* utilities */,
				2D09A29913AE084500C79C6A /* xtest */,
			);
			name = "toolbox-mfiles";
			sourceTree = "<group>";
		};
		2D72EA930E4776FE005DAA47 /* html */ = {
			isa = PBXGroup;
			children = (
				2DAF7CE3139918EA00FA0D07 /* about.html */,
				2D7AD0AA0E38C18600783252 /* api.html */,
				2DAF7CE4139918EA00FA0D07 /* apps.html */,
				2DAF7CE5139918EA00FA0D07 /* compiling.html */,
				2DAF7CE6139918EA00FA0D07 /* doc.html */,
				2D7AD0AB0E38C18600783252 /* download.html */,
				2D99420410ECD6E300502DF6 /* index.html */,
				2D7AD0AF0E38C18600783252 /* index.html */,
				2DAF7CE8139918EA00FA0D07 /* install-c.html */,
				2DAF7CE9139918EA00FA0D07 /* install-matlab.html */,
				2D219082187099540094BCA2 /* install-octave.html */,
				2DAF7CEA139918EA00FA0D07 /* install-shell.html */,
				2D3E1F2C15FE93480035AA1E /* license.html */,
				2D219085187099550094BCA2 /* notfound.html */,
				2D219086187099550094BCA2 /* roadmap.html */,
				2D7AD0B30E38C18600783252 /* tutorials.html */,
				2D21909118709C190094BCA2 /* tutorials */,
				2DAF7CEE139918EA00FA0D07 /* using-gcc.html */,
				2DAF7CEF139918EA00FA0D07 /* using-vsexpress.html */,
				2DAF7CF0139918EA00FA0D07 /* using-xcode.html */,
			);
			name = html;
			sourceTree = "<group>";
		};
		2D732DE40CF8C2E40099B03C /* src */ = {
			isa = PBXGroup;
			children = (
				2D732DE50CF8C2E40099B03C /* aib.c */,
				2D6007FF1115C1FA0020963C /* check.h */,
				2D732DE60CF8C2E40099B03C /* generic-driver.h */,
				2D732DE80CF8C2E40099B03C /* mser.c */,
				2D732DEA0CF8C2E40099B03C /* sift.c */,
				2D29DD661608F12B00E74DF3 /* test_gauss_elimination.c */,
				2D732DEB0CF8C2E40099B03C /* test_getopt_long.c */,
				2D9AAB2E1769B6A300AFBCFC /* test_gmm.c */,
				2D624AD60FF9306700DB3122 /* test_heap-def.c */,
				2DD9A01F0E5A296700CE1DA1 /* test_host.c */,
				2D72EB080E48A934005DAA47 /* test_imopv.c */,
				2D9AAB311769B6A300AFBCFC /* test_mathop.c */,
				2D6DD0F4100F5E5E006AE152 /* test_mathop_abs.c */,
				2D9AAB2F1769B6A300AFBCFC /* test_mathop_fast_resqrt.tc */,
				2D9AAB301769B6A300AFBCFC /* test_mathop_fast_sqrt_ui.tc */,
				2D732DEC0CF8C2E40099B03C /* test_nan.c */,
				2DBD0AA410F7A48C004DBA31 /* test_qsort-def.c */,
				2D9AAB321769B6A300AFBCFC /* test_rand.c */,
				2D732DED0CF8C2E40099B03C /* test_stringop.c */,
				2D9AAB331769B6A300AFBCFC /* test_svd2.c */,
				2D471AA710DD2C7900FA4182 /* test_threads.c */,
				2D13EEF8100A5CFE00C072E8 /* test_vec_comp.c */,
				2D4938AE1895ACE400B775EE /* test_sqrti.c */,
			);
			path = src;
			sourceTree = "<group>";
		};
		2D732E7D0CF8C2E40099B03C /* vl */ = {
			isa = PBXGroup;
			children = (
				2DE7564116D543EC0016C1DC /* clustering and indexing */,
				2DE7564016D543CA0016C1DC /* features */,
				2DE7564316D544740016C1DC /* image processing */,
				2DE7564216D544490016C1DC /* segmentation */,
				2DBD0B2110F7AB58004DBA31 /* metaprograms */,
				2D28A00116D540D100E81844 /* statistical methods */,
				2DFA36D212F1A26D00E808D9 /* array.c */,
				2DFA36D312F1A26D00E808D9 /* array.h */,
				2D0E6C4B1003E0DF00F0864E /* float.th */,
				2D732E800CF8C2E40099B03C /* generic.c */,
				2D732E810CF8C2E40099B03C /* generic.h */,
				2D732E820CF8C2E40099B03C /* getopt_long.c */,
				2D732E830CF8C2E40099B03C /* getopt_long.h */,
				2DD99E900E59EA8E00CE1DA1 /* host.c */,
				2DD99E8F0E59EA8E00CE1DA1 /* host.h */,
				2D732E8E0CF8C2E40099B03C /* mathop.c */,
				2D732E8F0CF8C2E40099B03C /* mathop.h */,
				2D117997178C1EA900311182 /* mathop_avx.c */,
				2D117998178C1EA900311182 /* mathop_avx.h */,
				2D13EEE1100A511200C072E8 /* mathop_sse2.c */,
				2D13EEE4100A539200C072E8 /* mathop_sse2.h */,
				2D732E910CF8C2E40099B03C /* mser.h */,
				2DD302780DE33107009443C7 /* random.c */,
				2DD302790DE33107009443C7 /* random.h */,
				2D732E940CF8C2E40099B03C /* rodrigues.c */,
				2D732E950CF8C2E40099B03C /* rodrigues.h */,
				2D732E980CF8C2E40099B03C /* stringop.c */,
				2D732E990CF8C2E40099B03C /* stringop.h */,
			);
			path = vl;
			sourceTree = "<group>";
		};
		2D732EAF0CF8C3950099B03C /* toolbox */ = {
			isa = PBXGroup;
			children = (
				2D1EED1816046F1C00C63DCE /* vl_simdctrl.c */,
				2DFA23A6173ECE7C0065603E /* vl_threads.c */,
				2D70CC780DDE1135000A23DE /* mexutils.h */,
				2D28A00516D5425B00E81844 /* clustering and indexing */,
				2D28A00416D5422500E81844 /* features */,
				2D28A00716D542C200E81844 /* image processing */,
				2D28A00616D5429C00E81844 /* segmentation */,
				2D28A00216D540DD00E81844 /* statistical methods */,
				2D10471216D54938001677AD /* utilities */,
			);
			name = toolbox;
			sourceTree = "<group>";
		};
		2D7AD0870E38C15700783252 /* doc */ = {
			isa = PBXGroup;
			children = (
				2D219088187099550094BCA2 /* vlfeat-website-main-content.xml */,
				2D219089187099550094BCA2 /* vlfeat-website-preproc.xml */,
				2D21908A187099550094BCA2 /* vlfeat-website-template.xml */,
				2D21908B187099550094BCA2 /* vlfeat-website.xml */,
				2D21908C187099550094BCA2 /* vlfeat.css */,
				2DAF7CEC139918EA00FA0D07 /* pygmentize.css */,
				2D708F261493C5540079605B /* doxygen.css */,
				2D3E1F2E15FE93480035AA1E /* vlfeat.bib */,
				2D7AD0AC0E38C18600783252 /* doxygen.conf */,
				2D7AD0800E38911C00783252 /* formatter.py */,
				2D7AD0810E38911C00783252 /* mdoc.py */,
				2D21907F187099540094BCA2 /* doxytag.py */,
				2D7AD0820E38911C00783252 /* webdoc.py */,
				2D7AD0830E38911C00783252 /* wikidoc.py */,
				2D72EA930E4776FE005DAA47 /* html */,
			);
			name = doc;
			sourceTree = "<group>";
		};
		2DBD0B2110F7AB58004DBA31 /* metaprograms */ = {
			isa = PBXGroup;
			children = (
				2D9941F410ECD04F00502DF6 /* heap-def.h */,
				2D4EB14D10F3F3A500ADA534 /* shuffle-def.h */,
				2DBD0AA110F78FEB004DBA31 /* qsort-def.h */,
			);
			name = metaprograms;
			sourceTree = "<group>";
		};
		2DE7564016D543CA0016C1DC /* features */ = {
			isa = PBXGroup;
			children = (
				2DA64CD217329C2400276F3D /* fisher.c */,
				2DA64CD317329C2400276F3D /* fisher.h */,
				2DA64CD717329C2400276F3D /* vlad.c */,
				2DA64CD817329C2400276F3D /* vlad.h */,
				2D732E900CF8C2E40099B03C /* mser.c */,
				2D1EED211604FE5900C63DCE /* covdet.c */,
				2D1EED221604FE5900C63DCE /* covdet.h */,
				2DE5B37D0FDC2BE9008CEB1D /* dsift.c */,
				2DE5B37E0FDC2BE9008CEB1D /* dsift.h */,
				2D85DE1414CD78BB00BDAE4E /* hog.c */,
				2D85DE1314CD78AC00BDAE4E /* hog.h */,
				2DFA23A2173B7F1C0065603E /* liop.c */,
				2DFA23A3173B7F1C0065603E /* liop.h */,
				2DDA2307124BD104003F6A9D /* lbp.c */,
				2DDA2306124BD104003F6A9D /* lbp.h */,
				2D1EED1D1604992A00C63DCE /* scalespace.c */,
				2D1EED1E1604992A00C63DCE /* scalespace.h */,
				2D732E960CF8C2E40099B03C /* sift.c */,
				2D732E970CF8C2E40099B03C /* sift.h */,
			);
			name = features;
			sourceTree = "<group>";
		};
		2DE7564116D543EC0016C1DC /* clustering and indexing */ = {
			isa = PBXGroup;
			children = (
				2DA64CD417329C2400276F3D /* gmm.c */,
				2DA64CD517329C2400276F3D /* gmm.h */,
				2D732E840CF8C2E40099B03C /* hikmeans.c */,
				2D732E850CF8C2E40099B03C /* hikmeans.h */,
				2D732E860CF8C2E40099B03C /* ikmeans.c */,
				2D732E870CF8C2E40099B03C /* ikmeans.h */,
				2D732E880CF8C2E40099B03C /* ikmeans_elkan.tc */,
				2D732E890CF8C2E40099B03C /* ikmeans_init.tc */,
				2D732E8A0CF8C2E40099B03C /* ikmeans_lloyd.tc */,
				2D765BAD0FEC076700D08578 /* kdtree.c */,
				2D765BAE0FEC076700D08578 /* kdtree.h */,
				2D4EB0BF10F3C1E800ADA534 /* kmeans.c */,
				2D4EB0C010F3C1E800ADA534 /* kmeans.h */,
			);
			name = "clustering and indexing";
			sourceTree = "<group>";
		};
		2DE7564216D544490016C1DC /* segmentation */ = {
			isa = PBXGroup;
			children = (
				2D765BAF0FEC076700D08578 /* quickshift.c */,
				2D765BB00FEC076700D08578 /* quickshift.h */,
				2D94E6BC148E48440089ADA5 /* slic.c */,
				2D94E6BE1490E4120089ADA5 /* slic.h */,
			);
			name = segmentation;
			sourceTree = "<group>";
		};
		2DE7564316D544740016C1DC /* image processing */ = {
			isa = PBXGroup;
			children = (
				2D732E920CF8C2E40099B03C /* pgm.c */,
				2D732E930CF8C2E40099B03C /* pgm.h */,
				2D72EAF00E48A42F005DAA47 /* imopv.c */,
				2D72EAF10E48A42F005DAA47 /* imopv.h */,
				2DD99CC40E58A86B00CE1DA1 /* imopv_sse2.c */,
				2DD99CC50E58A8C700CE1DA1 /* imopv_sse2.h */,
			);
			name = "image processing";
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		2D0E8A7D1786D18D005419DC /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D1EECD01603E85200C63DCE /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2D1EED201604992B00C63DCE /* scalespace.h in Headers */,
				2DA64CDA17329C2400276F3D /* fisher.h in Headers */,
				2DA64CDC17329C2400276F3D /* gmm.h in Headers */,
				2DA64CDD17329C2400276F3D /* svm.h in Headers */,
				2DA64CDF17329C2400276F3D /* vlad.h in Headers */,
				2DFA23A5173B7F1C0065603E /* liop.h in Headers */,
				2D11799A178C1EA900311182 /* mathop_avx.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D81B9B21735666E000706C0 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2DA47BD51792E6C700EC02E0 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2DB3F0571605EC0B00862CCA /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2DD9AD1A17F9C41200C1FC78 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2DE607621785722900E1A24E /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2DFA239A173B7D0F0065603E /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXLegacyTarget section */
		2D21908D187099F20094BCA2 /* make doc */ = {
			isa = PBXLegacyTarget;
			buildArgumentsString = doc;
			buildConfigurationList = 2D21908E187099F20094BCA2 /* Build configuration list for PBXLegacyTarget "make doc" */;
			buildPhases = (
			);
			buildToolPath = /usr/bin/make;
			buildWorkingDirectory = "";
			dependencies = (
			);
			name = "make doc";
			passBuildSettingsInEnvironment = 1;
			productName = "vlfeat-all";
		};
		2D72E9C90E4765D2005DAA47 /* make doc-api */ = {
			isa = PBXLegacyTarget;
			buildArgumentsString = "doc-api";
			buildConfigurationList = 2D72E9CA0E4765D2005DAA47 /* Build configuration list for PBXLegacyTarget "make doc-api" */;
			buildPhases = (
			);
			buildToolPath = /usr/bin/make;
			buildWorkingDirectory = "";
			dependencies = (
			);
			name = "make doc-api";
			passBuildSettingsInEnvironment = 1;
			productName = "vlfeat-all";
		};
		2D72EA220E476C05005DAA47 /* make info */ = {
			isa = PBXLegacyTarget;
			buildArgumentsString = info;
			buildConfigurationList = 2D72EA230E476C05005DAA47 /* Build configuration list for PBXLegacyTarget "make info" */;
			buildPhases = (
			);
			buildToolPath = /usr/bin/make;
			buildWorkingDirectory = "";
			dependencies = (
			);
			name = "make info";
			passBuildSettingsInEnvironment = 1;
			productName = "vlfeat-all";
		};
		2D732DBE0CF8BFFD0099B03C /* make */ = {
			isa = PBXLegacyTarget;
			buildArgumentsString = "$(ACTION)";
			buildConfigurationList = 2D732DCF0CF8C02D0099B03C /* Build configuration list for PBXLegacyTarget "make" */;
			buildPhases = (
			);
			buildToolPath = /usr/bin/make;
			buildWorkingDirectory = "";
			dependencies = (
			);
			name = make;
			passBuildSettingsInEnvironment = 1;
			productName = "vlfeat-all";
		};
/* End PBXLegacyTarget section */

/* Begin PBXNativeTarget section */
		2D0E8A781786D18D005419DC /* vl_fisher */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2D0E8A7E1786D18D005419DC /* Build configuration list for PBXNativeTarget "vl_fisher" */;
			buildPhases = (
				2D0E8A791786D18D005419DC /* Sources */,
				2D0E8A7B1786D18D005419DC /* Frameworks */,
				2D0E8A7D1786D18D005419DC /* Headers */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = vl_fisher;
			productName = vl_covdet;
			productReference = 2D0E8A811786D18D005419DC /* vl_fisher.mexmaci64 */;
			productType = "com.apple.product-type.library.dynamic";
		};
		2D1EECD11603E85200C63DCE /* vl */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2D1EECD31603E85200C63DCE /* Build configuration list for PBXNativeTarget "vl" */;
			buildPhases = (
				2D1EECCE1603E85200C63DCE /* Sources */,
				2D1EECCF1603E85200C63DCE /* Frameworks */,
				2D1EECD01603E85200C63DCE /* Headers */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = vl;
			productName = vl;
			productReference = 2D1EECD21603E85200C63DCE /* libvl.dylib */;
			productType = "com.apple.product-type.library.dynamic";
		};
		2D81B9AD1735666E000706C0 /* vl_svmtrain */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2D81B9B31735666E000706C0 /* Build configuration list for PBXNativeTarget "vl_svmtrain" */;
			buildPhases = (
				2D81B9AE1735666E000706C0 /* Sources */,
				2D81B9B01735666E000706C0 /* Frameworks */,
				2D81B9B21735666E000706C0 /* Headers */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = vl_svmtrain;
			productName = vl_covdet;
			productReference = 2D81B9B61735666E000706C0 /* vl_svmtrain.mexmaci64 */;
			productType = "com.apple.product-type.library.dynamic";
		};
		2DA47BD01792E6C700EC02E0 /* vl_liop */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2DA47BD61792E6C700EC02E0 /* Build configuration list for PBXNativeTarget "vl_liop" */;
			buildPhases = (
				2DA47BD11792E6C700EC02E0 /* Sources */,
				2DA47BD31792E6C700EC02E0 /* Frameworks */,
				2DA47BD51792E6C700EC02E0 /* Headers */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = vl_liop;
			productName = vl_covdet;
			productReference = 2DA47BD91792E6C700EC02E0 /* vl_liop.mexmaci64 */;
			productType = "com.apple.product-type.library.dynamic";
		};
		2DB3F0581605EC0B00862CCA /* vl_covdet */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2DB3F05A1605EC0B00862CCA /* Build configuration list for PBXNativeTarget "vl_covdet" */;
			buildPhases = (
				2DB3F0551605EC0B00862CCA /* Sources */,
				2DB3F0561605EC0B00862CCA /* Frameworks */,
				2DB3F0571605EC0B00862CCA /* Headers */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = vl_covdet;
			productName = vl_covdet;
			productReference = 2DB3F0591605EC0B00862CCA /* vl_covdet.mexmaci64 */;
			productType = "com.apple.product-type.library.dynamic";
		};
		2DD9AD1517F9C41200C1FC78 /* vl_kmeans */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2DD9AD1B17F9C41200C1FC78 /* Build configuration list for PBXNativeTarget "vl_kmeans" */;
			buildPhases = (
				2DD9AD1617F9C41200C1FC78 /* Sources */,
				2DD9AD1817F9C41200C1FC78 /* Frameworks */,
				2DD9AD1A17F9C41200C1FC78 /* Headers */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = vl_kmeans;
			productName = vl_covdet;
			productReference = 2DD9AD1E17F9C41200C1FC78 /* vl_kmeans.mexmaci64 */;
			productType = "com.apple.product-type.library.dynamic";
		};
		2DE6075D1785722900E1A24E /* vl_vlad */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2DE607631785722900E1A24E /* Build configuration list for PBXNativeTarget "vl_vlad" */;
			buildPhases = (
				2DE6075E1785722900E1A24E /* Sources */,
				2DE607601785722900E1A24E /* Frameworks */,
				2DE607621785722900E1A24E /* Headers */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = vl_vlad;
			productName = vl_covdet;
			productReference = 2DE607661785722900E1A24E /* vl_vlad.mexmaci64 */;
			productType = "com.apple.product-type.library.dynamic";
		};
		2DFA2395173B7D0F0065603E /* vl_gmm */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2DFA239B173B7D0F0065603E /* Build configuration list for PBXNativeTarget "vl_gmm" */;
			buildPhases = (
				2DFA2396173B7D0F0065603E /* Sources */,
				2DFA2398173B7D0F0065603E /* Frameworks */,
				2DFA239A173B7D0F0065603E /* Headers */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = vl_gmm;
			productName = vl_covdet;
			productReference = 2DFA239E173B7D0F0065603E /* vl_gmm.mexmaci64 */;
			productType = "com.apple.product-type.library.dynamic";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		08FB7793FE84155DC02AAC07 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0500;
			};
			buildConfigurationList = 1DEB919308733D9F0010E9CD /* Build configuration list for PBXProject "vlfeat" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 1;
			knownRegions = (
				en,
			);
			mainGroup = 08FB7794FE84155DC02AAC07 /* vlfeat */;
			productRefGroup = 08FB7794FE84155DC02AAC07 /* vlfeat */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				2D732DBE0CF8BFFD0099B03C /* make */,
				2D72E9C90E4765D2005DAA47 /* make doc-api */,
				2D21908D187099F20094BCA2 /* make doc */,
				2D72EA220E476C05005DAA47 /* make info */,
				2D1EECD11603E85200C63DCE /* vl */,
				2DB3F0581605EC0B00862CCA /* vl_covdet */,
				2D81B9AD1735666E000706C0 /* vl_svmtrain */,
				2DFA2395173B7D0F0065603E /* vl_gmm */,
				2DE6075D1785722900E1A24E /* vl_vlad */,
				2D0E8A781786D18D005419DC /* vl_fisher */,
				2DA47BD01792E6C700EC02E0 /* vl_liop */,
				2DD9AD1517F9C41200C1FC78 /* vl_kmeans */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		2D0E8A791786D18D005419DC /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2DD9AD2017F9C44A00C1FC78 /* vl_fisher.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D1EECCE1603E85200C63DCE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2D1EECD61603E8E900C63DCE /* aib.c in Sources */,
				2D1EECD71603E8E900C63DCE /* array.c in Sources */,
				2D1EECD81603E8E900C63DCE /* dsift.c in Sources */,
				2D1EECD91603E8E900C63DCE /* generic.c in Sources */,
				2D1EECDA1603E8E900C63DCE /* getopt_long.c in Sources */,
				2D1EECDB1603E8E900C63DCE /* hikmeans.c in Sources */,
				2D1EECDC1603E8E900C63DCE /* hog.c in Sources */,
				2D1EECDD1603E8E900C63DCE /* homkermap.c in Sources */,
				2D1EECDE1603E8E900C63DCE /* host.c in Sources */,
				2D1EECDF1603E8E900C63DCE /* ikmeans.c in Sources */,
				2D1EECE01603E8E900C63DCE /* imopv.c in Sources */,
				2D1EECE11603E8E900C63DCE /* imopv_sse2.c in Sources */,
				2D4938AF1895ACE400B775EE /* test_sqrti.c in Sources */,
				2D1EECE21603E8E900C63DCE /* kdtree.c in Sources */,
				2D1EECE31603E8E900C63DCE /* kmeans.c in Sources */,
				2D1EECE41603E8E900C63DCE /* lbp.c in Sources */,
				2D1EECE51603E8E900C63DCE /* mathop.c in Sources */,
				2D1EECE61603E8E900C63DCE /* mathop_sse2.c in Sources */,
				2D1EECE71603E8E900C63DCE /* mser.c in Sources */,
				2D1EECE91603E8E900C63DCE /* pgm.c in Sources */,
				2D1EECEA1603E8E900C63DCE /* quickshift.c in Sources */,
				2D1EECEB1603E8E900C63DCE /* random.c in Sources */,
				2D1EECEC1603E8E900C63DCE /* rodrigues.c in Sources */,
				2D1EECED1603E8E900C63DCE /* sift.c in Sources */,
				2D1EECEE1603E8E900C63DCE /* slic.c in Sources */,
				2D1EECEF1603E8E900C63DCE /* stringop.c in Sources */,
				2D1EECF01603E8E900C63DCE /* svmdataset.c in Sources */,
				2D1EED1F1604992B00C63DCE /* scalespace.c in Sources */,
				2D1BC0F6160DF170009E8DD3 /* covdet.c in Sources */,
				2DA64CD917329C2400276F3D /* fisher.c in Sources */,
				2DA64CDB17329C2400276F3D /* gmm.c in Sources */,
				2DA64CDE17329C2400276F3D /* vlad.c in Sources */,
				2DA64CE11733089000276F3D /* svm.c in Sources */,
				2DFA23A4173B7F1C0065603E /* liop.c in Sources */,
				2D117999178C1EA900311182 /* mathop_avx.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2D81B9AE1735666E000706C0 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2D81B9B717356697000706C0 /* vl_svmtrain.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2DA47BD11792E6C700EC02E0 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2D63C3A917969032001C6AE0 /* vl_liop.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2DB3F0551605EC0B00862CCA /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2DB3F05D1605ED2300862CCA /* vl_covdet.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2DD9AD1617F9C41200C1FC78 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2DD9AD1F17F9C43700C1FC78 /* vl_kmeans.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2DE6075E1785722900E1A24E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2DE607671785725200E1A24E /* vl_vlad.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2DFA2396173B7D0F0065603E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2DFA23A1173B7E730065603E /* vl_gmm.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		2D0E8A7F1786D18D005419DC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				EXECUTABLE_EXTENSION = mexmaci64;
				EXECUTABLE_PREFIX = "";
				EXPORTED_SYMBOLS_FILE = "$(MATLABROOT)/extern/lib/maci64/mexFunction.map";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				LIBRARY_SEARCH_PATHS = "$(MATLABROOT)/bin/maci64";
				MACOSX_DEPLOYMENT_TARGET = 10.8;
				OTHER_LDFLAGS = (
					"-lmx",
					"-lmex",
					"-lmat",
					"-lstdc++",
				);
				PRODUCT_NAME = vl_fisher;
			};
			name = Debug;
		};
		2D0E8A801786D18D005419DC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				EXECUTABLE_EXTENSION = mexmaci64;
				EXECUTABLE_PREFIX = "";
				EXPORTED_SYMBOLS_FILE = "$(MATLABROOT)/extern/lib/maci64/mexFunction.map";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				LIBRARY_SEARCH_PATHS = "$(MATLABROOT)/bin/maci64";
				MACOSX_DEPLOYMENT_TARGET = 10.8;
				OTHER_LDFLAGS = (
					"-lmx",
					"-lmex",
					"-lmat",
					"-lstdc++",
				);
				PRODUCT_NAME = vl_fisher;
			};
			name = Release;
		};
		2D1EECD41603E85200C63DCE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				EXECUTABLE_PREFIX = lib;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.8;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		2D1EECD51603E85200C63DCE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				EXECUTABLE_PREFIX = lib;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.8;
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
		2D21908F187099F20094BCA2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				PRODUCT_NAME = "make doc-api copy";
			};
			name = Debug;
		};
		2D219090187099F20094BCA2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
				PRODUCT_NAME = "make doc-api copy";
			};
			name = Release;
		};
		2D81B9B41735666E000706C0 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				EXECUTABLE_EXTENSION = mexmaci64;
				EXECUTABLE_PREFIX = "";
				EXPORTED_SYMBOLS_FILE = "$(MATLABROOT)/extern/lib/maci64/mexFunction.map";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				LIBRARY_SEARCH_PATHS = "$(MATLABROOT)/bin/maci64";
				MACOSX_DEPLOYMENT_TARGET = 10.8;
				OTHER_LDFLAGS = (
					"-lmx",
					"-lmex",
					"-lmat",
					"-lstdc++",
				);
				PRODUCT_NAME = vl_svmtrain;
			};
			name = Debug;
		};
		2D81B9B51735666E000706C0 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				EXECUTABLE_EXTENSION = mexmaci64;
				EXECUTABLE_PREFIX = "";
				EXPORTED_SYMBOLS_FILE = "$(MATLABROOT)/extern/lib/maci64/mexFunction.map";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				LIBRARY_SEARCH_PATHS = "$(MATLABROOT)/bin/maci64";
				MACOSX_DEPLOYMENT_TARGET = 10.8;
				OTHER_LDFLAGS = (
					"-lmx",
					"-lmex",
					"-lmat",
					"-lstdc++",
				);
				PRODUCT_NAME = vl_svmtrain;
			};
			name = Release;
		};
		2D8E1B7C13720C55009CDE11 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ACTIVE_TEST = test_host;
				ARCH = "$(ARCH_$(ARCHS))";
				ARCH_i386 = maci;
				ARCH_x86_64 = maci64;
				CC = gcc;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_IMPLICIT_SIGN_CONVERSION = NO;
				CLANG_WARN_SUSPICIOUS_IMPLICIT_CONVERSION = NO;
				CLANG_X86_VECTOR_INSTRUCTIONS = avx;
				DEBUG = YES;
				DOXYGEN = doxygen;
				GCC_TREAT_IMPLICIT_FUNCTION_DECLARATIONS_AS_ERRORS = YES;
				GCC_TREAT_INCOMPATIBLE_POINTER_TYPE_WARNINGS_AS_ERRORS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_MISSING_FIELD_INITIALIZERS = YES;
				GCC_WARN_ABOUT_MISSING_NEWLINE = YES;
				GCC_WARN_ABOUT_MISSING_PROTOTYPES = NO;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_FOUR_CHARACTER_CONSTANTS = YES;
				GCC_WARN_INITIALIZER_NOT_FULLY_BRACKETED = YES;
				GCC_WARN_PEDANTIC = NO;
				GCC_WARN_SHADOW = NO;
				GCC_WARN_SIGN_COMPARE = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNKNOWN_PRAGMAS = YES;
				GCC_WARN_UNUSED_FUNCTION = NO;
				GCC_WARN_UNUSED_LABEL = NO;
				GCC_WARN_UNUSED_PARAMETER = NO;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(SRCROOT)",
					"$(SRCROOT)/toolbox",
					"$(MATLABROOT)/extern/include",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.4;
				MATLABEXE = "$(MATLABROOT)/bin/matlab";
				MATLABROOT = /Applications/Matlab_R2013A.app;
				MEX = "$(MATLABROOT)/bin/mex";
				ONLY_ACTIVE_ARCH = YES;
				PATH = "$(PATH):/opt/local/bin/";
				PROFILE = "";
				PYTHON = "MACOSX_DEPLOYMENT_TARGET=10.6 python";
				SDKROOT = macosx;
				VERB = YES;
				VLDIR = "$(PROJECT_DIR)";
			};
			name = Debug;
		};
		2D8E1B7D13720C55009CDE11 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
			};
			name = Debug;
		};
		2D8E1B7E13720C55009CDE11 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
			};
			name = Debug;
		};
		2D8E1B7F13720C55009CDE11 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
			};
			name = Debug;
		};
		2D8E1B8013720C5E009CDE11 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ACTIVE_TEST = test_host;
				ARCH = "$(ARCH_$(ARCHS))";
				ARCH_i386 = maci;
				ARCH_x86_64 = maci64;
				CC = gcc;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_IMPLICIT_SIGN_CONVERSION = NO;
				CLANG_WARN_SUSPICIOUS_IMPLICIT_CONVERSION = NO;
				CLANG_X86_VECTOR_INSTRUCTIONS = avx;
				DEBUG = "";
				DOXYGEN = doxygen;
				GCC_TREAT_IMPLICIT_FUNCTION_DECLARATIONS_AS_ERRORS = YES;
				GCC_TREAT_INCOMPATIBLE_POINTER_TYPE_WARNINGS_AS_ERRORS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_MISSING_FIELD_INITIALIZERS = YES;
				GCC_WARN_ABOUT_MISSING_NEWLINE = YES;
				GCC_WARN_ABOUT_MISSING_PROTOTYPES = NO;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_FOUR_CHARACTER_CONSTANTS = YES;
				GCC_WARN_INITIALIZER_NOT_FULLY_BRACKETED = YES;
				GCC_WARN_PEDANTIC = NO;
				GCC_WARN_SHADOW = NO;
				GCC_WARN_SIGN_COMPARE = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNKNOWN_PRAGMAS = YES;
				GCC_WARN_UNUSED_FUNCTION = NO;
				GCC_WARN_UNUSED_LABEL = NO;
				GCC_WARN_UNUSED_PARAMETER = NO;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(SRCROOT)",
					"$(SRCROOT)/toolbox",
					"$(MATLABROOT)/extern/include",
				);
				MACOSX_DEPLOYMENT_TARGET = 10.4;
				MATLABEXE = "$(MATLABROOT)/bin/matlab";
				MATLABROOT = /Applications/Matlab_R2013A.app;
				MEX = "$(MATLABROOT)/bin/mex";
				ONLY_ACTIVE_ARCH = YES;
				PATH = "$(PATH):/opt/local/bin/";
				PROFILE = YES;
				PYTHON = "MACOSX_DEPLOYMENT_TARGET=10.6 python";
				SDKROOT = macosx;
				VERB = YES;
				VLDIR = "$(PROJECT_DIR)";
			};
			name = Release;
		};
		2D8E1B8113720C5E009CDE11 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
			};
			name = Release;
		};
		2D8E1B8213720C5E009CDE11 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
			};
			name = Release;
		};
		2D8E1B8313720C5E009CDE11 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				COMBINE_HIDPI_IMAGES = YES;
			};
			name = Release;
		};
		2DA47BD71792E6C700EC02E0 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				EXECUTABLE_EXTENSION = mexmaci64;
				EXECUTABLE_PREFIX = "";
				EXPORTED_SYMBOLS_FILE = "$(MATLABROOT)/extern/lib/maci64/mexFunction.map";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				LIBRARY_SEARCH_PATHS = "$(MATLABROOT)/bin/maci64";
				MACOSX_DEPLOYMENT_TARGET = 10.8;
				OTHER_LDFLAGS = (
					"-lmx",
					"-lmex",
					"-lmat",
					"-lstdc++",
				);
				PRODUCT_NAME = vl_liop;
			};
			name = Debug;
		};
		2DA47BD81792E6C700EC02E0 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				EXECUTABLE_EXTENSION = mexmaci64;
				EXECUTABLE_PREFIX = "";
				EXPORTED_SYMBOLS_FILE = "$(MATLABROOT)/extern/lib/maci64/mexFunction.map";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				LIBRARY_SEARCH_PATHS = "$(MATLABROOT)/bin/maci64";
				MACOSX_DEPLOYMENT_TARGET = 10.8;
				OTHER_LDFLAGS = (
					"-lmx",
					"-lmex",
					"-lmat",
					"-lstdc++",
				);
				PRODUCT_NAME = vl_liop;
			};
			name = Release;
		};
		2DB3F05B1605EC0B00862CCA /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				EXECUTABLE_EXTENSION = mexmaci64;
				EXECUTABLE_PREFIX = "";
				EXPORTED_SYMBOLS_FILE = "$(MATLABROOT)/extern/lib/maci64/mexFunction.map";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				LIBRARY_SEARCH_PATHS = "$(MATLABROOT)/bin/maci64";
				MACOSX_DEPLOYMENT_TARGET = 10.8;
				OTHER_LDFLAGS = (
					"-lmx",
					"-lmex",
					"-lmat",
					"-lstdc++",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		2DB3F05C1605EC0B00862CCA /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				EXECUTABLE_EXTENSION = mexmaci64;
				EXECUTABLE_PREFIX = "";
				EXPORTED_SYMBOLS_FILE = "$(MATLABROOT)/extern/lib/maci64/mexFunction.map";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				LIBRARY_SEARCH_PATHS = "$(MATLABROOT)/bin/maci64";
				MACOSX_DEPLOYMENT_TARGET = 10.8;
				OTHER_LDFLAGS = (
					"-lmx",
					"-lmex",
					"-lmat",
					"-lstdc++",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
		2DD9AD1C17F9C41200C1FC78 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				EXECUTABLE_EXTENSION = mexmaci64;
				EXECUTABLE_PREFIX = "";
				EXPORTED_SYMBOLS_FILE = "$(MATLABROOT)/extern/lib/maci64/mexFunction.map";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				LIBRARY_SEARCH_PATHS = "$(MATLABROOT)/bin/maci64";
				MACOSX_DEPLOYMENT_TARGET = 10.8;
				OTHER_LDFLAGS = (
					"-lmx",
					"-lmex",
					"-lmat",
					"-lstdc++",
				);
				PRODUCT_NAME = vl_kmeans;
			};
			name = Debug;
		};
		2DD9AD1D17F9C41200C1FC78 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				EXECUTABLE_EXTENSION = mexmaci64;
				EXECUTABLE_PREFIX = "";
				EXPORTED_SYMBOLS_FILE = "$(MATLABROOT)/extern/lib/maci64/mexFunction.map";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				LIBRARY_SEARCH_PATHS = "$(MATLABROOT)/bin/maci64";
				MACOSX_DEPLOYMENT_TARGET = 10.8;
				OTHER_LDFLAGS = (
					"-lmx",
					"-lmex",
					"-lmat",
					"-lstdc++",
				);
				PRODUCT_NAME = vl_kmeans;
			};
			name = Release;
		};
		2DE607641785722900E1A24E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				EXECUTABLE_EXTENSION = mexmaci64;
				EXECUTABLE_PREFIX = "";
				EXPORTED_SYMBOLS_FILE = "$(MATLABROOT)/extern/lib/maci64/mexFunction.map";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				LIBRARY_SEARCH_PATHS = "$(MATLABROOT)/bin/maci64";
				MACOSX_DEPLOYMENT_TARGET = 10.8;
				OTHER_LDFLAGS = (
					"-lmx",
					"-lmex",
					"-lmat",
					"-lstdc++",
				);
				PRODUCT_NAME = vl_vlad;
			};
			name = Debug;
		};
		2DE607651785722900E1A24E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				EXECUTABLE_EXTENSION = mexmaci64;
				EXECUTABLE_PREFIX = "";
				EXPORTED_SYMBOLS_FILE = "$(MATLABROOT)/extern/lib/maci64/mexFunction.map";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				LIBRARY_SEARCH_PATHS = "$(MATLABROOT)/bin/maci64";
				MACOSX_DEPLOYMENT_TARGET = 10.8;
				OTHER_LDFLAGS = (
					"-lmx",
					"-lmex",
					"-lmat",
					"-lstdc++",
				);
				PRODUCT_NAME = vl_vlad;
			};
			name = Release;
		};
		2DFA239C173B7D0F0065603E /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				EXECUTABLE_EXTENSION = mexmaci64;
				EXECUTABLE_PREFIX = "";
				EXPORTED_SYMBOLS_FILE = "$(MATLABROOT)/extern/lib/maci64/mexFunction.map";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				LIBRARY_SEARCH_PATHS = "$(MATLABROOT)/bin/maci64";
				MACOSX_DEPLOYMENT_TARGET = 10.8;
				OTHER_LDFLAGS = (
					"-lmx",
					"-lmex",
					"-lmat",
					"-lstdc++",
				);
				PRODUCT_NAME = vl_gmm;
			};
			name = Debug;
		};
		2DFA239D173B7D0F0065603E /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				EXECUTABLE_EXTENSION = mexmaci64;
				EXECUTABLE_PREFIX = "";
				EXPORTED_SYMBOLS_FILE = "$(MATLABROOT)/extern/lib/maci64/mexFunction.map";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				LIBRARY_SEARCH_PATHS = "$(MATLABROOT)/bin/maci64";
				MACOSX_DEPLOYMENT_TARGET = 10.8;
				OTHER_LDFLAGS = (
					"-lmx",
					"-lmex",
					"-lmat",
					"-lstdc++",
				);
				PRODUCT_NAME = vl_gmm;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1DEB919308733D9F0010E9CD /* Build configuration list for PBXProject "vlfeat" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2D8E1B7C13720C55009CDE11 /* Debug */,
				2D8E1B8013720C5E009CDE11 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2D0E8A7E1786D18D005419DC /* Build configuration list for PBXNativeTarget "vl_fisher" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2D0E8A7F1786D18D005419DC /* Debug */,
				2D0E8A801786D18D005419DC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2D1EECD31603E85200C63DCE /* Build configuration list for PBXNativeTarget "vl" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2D1EECD41603E85200C63DCE /* Debug */,
				2D1EECD51603E85200C63DCE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2D21908E187099F20094BCA2 /* Build configuration list for PBXLegacyTarget "make doc" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2D21908F187099F20094BCA2 /* Debug */,
				2D219090187099F20094BCA2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2D72E9CA0E4765D2005DAA47 /* Build configuration list for PBXLegacyTarget "make doc-api" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2D8E1B7E13720C55009CDE11 /* Debug */,
				2D8E1B8213720C5E009CDE11 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2D72EA230E476C05005DAA47 /* Build configuration list for PBXLegacyTarget "make info" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2D8E1B7F13720C55009CDE11 /* Debug */,
				2D8E1B8313720C5E009CDE11 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2D732DCF0CF8C02D0099B03C /* Build configuration list for PBXLegacyTarget "make" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2D8E1B7D13720C55009CDE11 /* Debug */,
				2D8E1B8113720C5E009CDE11 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2D81B9B31735666E000706C0 /* Build configuration list for PBXNativeTarget "vl_svmtrain" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2D81B9B41735666E000706C0 /* Debug */,
				2D81B9B51735666E000706C0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2DA47BD61792E6C700EC02E0 /* Build configuration list for PBXNativeTarget "vl_liop" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2DA47BD71792E6C700EC02E0 /* Debug */,
				2DA47BD81792E6C700EC02E0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2DB3F05A1605EC0B00862CCA /* Build configuration list for PBXNativeTarget "vl_covdet" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2DB3F05B1605EC0B00862CCA /* Debug */,
				2DB3F05C1605EC0B00862CCA /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2DD9AD1B17F9C41200C1FC78 /* Build configuration list for PBXNativeTarget "vl_kmeans" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2DD9AD1C17F9C41200C1FC78 /* Debug */,
				2DD9AD1D17F9C41200C1FC78 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2DE607631785722900E1A24E /* Build configuration list for PBXNativeTarget "vl_vlad" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2DE607641785722900E1A24E /* Debug */,
				2DE607651785722900E1A24E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2DFA239B173B7D0F0065603E /* Build configuration list for PBXNativeTarget "vl_gmm" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2DFA239C173B7D0F0065603E /* Debug */,
				2DFA239D173B7D0F0065603E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 08FB7793FE84155DC02AAC07 /* Project object */;
}
