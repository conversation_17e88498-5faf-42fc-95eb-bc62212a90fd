#pragma once
#include "ISolver.hpp"

namespace SWDC {
	namespace multiview {
		namespace relativePose {

			class PerspectiveSolver : public ISolver<Mat3>
			{
			public:
				/**
				 * @brief Return the minimum number of required samples
				 * @return minimum number of required samples
				 */
				inline std::size_t getMinimumNbRequiredSamples() const override
				{
					return std::numeric_limits<int>::infinity();
				}

				/**
				 * @brief Return the maximum number of models
				 * @return maximum number of models
				 */
				inline std::size_t getMaximumNbModels() const override
				{
					return 1;
				}


				/**
				* @brief Computes the relative pose of two calibrated cameras from 5 correspondences.
				* @param[in] x1 Points in the first image.  One per column.
				* @param[in] x2 Corresponding points in the second image. One per column.
				* @param[out] models  A list of at most 10 candidate essential matrix solutions.
				*/
				void solve(const Mat& x1, const Mat& x2, std::vector<Mat3>& models) const override;
			};

		}
	}
}


