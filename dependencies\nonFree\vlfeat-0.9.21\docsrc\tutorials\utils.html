<!DOCTYPE group PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<group>

<p>This is a list of other notable MATLAB functions included
in <b>VLFeat</b></p>

<ul>
<li><code>vl_imsmooth</code> smoothes an image by a Gaussian kernel
  (simple but very useful as it is generally much faster than MATLAB's
  general purpose smoothing functions).</li>

<li><code>vl_plotframe</code> plots a variety of feature frame types (such as oriented ellipses).</li>

<li><code>vl_binsum</code> performs binned summations, useful as a
  building block for the fast computation of
  histograms. <code>vl_whistc</code> computes weighed histograms.</li>
<li><code>vl_imarray</code> and <code>vl_imarraysc</code> arrange and visualize multiple
images in a grid</li>
<li><code>vl_imsc</code> scales the image range;</li>
<li><code>vl_tightsubplot</code> is similar to built-in
<code>subplot</code>, but produces narrower margins.</li>

<li><code>vl_cf</code> makes a copy of a figure.</li>

<li><code>vl_rodr</code> and <code>vl_irodr</code> compute the Rodrigues' formula and
  its inverse</li>

<li><code>vl_override</code> overrides members of a structure with members of
  another structure.</li>

<li><code>vl_imwbackward</code> warps an image by the inverse mapping method
  (generally much faster than MATLAB general purpose warping
  functions).</li>

<li><code>vl_waffine</code> computes the affine warp of a set of
  points. <code>vl_tps</code>, <code>vl_tpsu</code>, <code>vl_wtps</code>
  compute the thin-plate spline warp of a set of points. <code>vl_witps</code>
  computes the inverse thin plate warp of a set of point (by
  numerically inverting the transformation). They may be used in
  combination with <code>vl_imwbackward</code>.</li>

<li><code>vl_xyz2lab</code>,
  <code>vl_xyz2luv</code>, <code>vl_xyz2rgb</code>, <code>vl_rgb2xyz</code>
  convert color spaces.</li>

<li><code>vl_rcos</code>, <code>vl_gaussian</code>, <code>vl_dgaussian</code>,
<code>vl_ddgaussian</code>
compute some useful special functions.</li>

</ul>
</group>
