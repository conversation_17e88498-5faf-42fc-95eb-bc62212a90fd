import QtQuick 2.12
import QtQuick.Controls 2.14

TextInput {
    id: textInput
    property string placeholder: ""
    property string placeholderColor: "#757575"
    font.weight: Font.Light
    font.family: "Verdana"
    font.pointSize: 10
    verticalAlignment: Text.AlignVCenter
    horizontalAlignment: Text.AlignHCenter

    Text {
       id: placeholderText
       text: placeholder
       color: placeholderColor
       font.weight: Font.Light
       font.family: "Verdana"
       font.pixelSize: 12
       visible: false
       anchors.centerIn: parent
    }

    onActiveFocusChanged:{
        if(textInput.text === ""){
            placeholderText.visible = !placeholderText.visible;
        }
    }

    Component.onCompleted: {
        if(textInput.text === "" && placeholder != "") placeholderText.visible = true;
    }
}
