#context div.doxygen,
#content .doxygen table,
#content .doxygen div,
#content .doxygen p,
#content .doxygen dl {
}
#content .doxygen h1 {
  font-size: 150%;
}
#content .doxygen .title {
  font-size: 150%;
  font-weight: bold;
  margin: 10px 2px;
}
#content .doxygen h2 {
  font-size: 120%;
}
#content .doxygen h3 {
  font-size: 100%;
}
#content .doxygen h1,
#content .doxygen h2,
#content .doxygen h3,
#content .doxygen h4,
#content .doxygen h5,
#content .doxygen h6 {
  -webkit-transition: text-shadow 0.5s linear;
  -moz-transition: text-shadow 0.5s linear;
  -ms-transition: text-shadow 0.5s linear;
  -o-transition: text-shadow 0.5s linear;
  transition: text-shadow 0.5s linear;
  margin-right: 15px;
}
#content .doxygen h1.glow,
#content .doxygen h2.glow,
#content .doxygen h3.glow,
#content .doxygen h4.glow,
#content .doxygen h5.glow,
#content .doxygen h6.glow {
  text-shadow: 0 0 15px cyan;
}
#content .doxygen dt {
  font-weight: bold;
}
#content .doxygen div.multicol {
  -moz-column-gap: 1em;
  -webkit-column-gap: 1em;
  -moz-column-count: 3;
  -webkit-column-count: 3;
}
#content .doxygen p.startli,
#content .doxygen p.startdd,
#content .doxygen p.starttd {
  margin-top: 2px;
}
#content .doxygen p.endli {
  margin-bottom: 0px;
}
#content .doxygen p.enddd {
  margin-bottom: 4px;
}
#content .doxygen p.endtd {
  margin-bottom: 2px;
}
#content .doxygen caption {
  font-weight: bold;
}
#content .doxygen span.legend {
  font-size: 70%;
  text-align: center;
}
#content .doxygen h3.version {
  font-size: 90%;
  text-align: center;
}
#content .doxygen div.qindex,
#content .doxygen div.navtab {
  background-color: #EBEFF6;
  border: 1px solid #A3B4D7;
  text-align: center;
}
#content .doxygen div.qindex,
#content .doxygen div.navpath {
  width: 100%;
  line-height: 140%;
}
#content .doxygen div.navtab {
  margin-right: 15px;
}
#content .doxygen a {
  color: #3D578C;
  font-weight: normal;
  text-decoration: none;
}
#content .doxygen .contents a:visited {
  color: #4665A2;
}
#content .doxygen a:hover {
  text-decoration: underline;
}
#content .doxygen a.qindex {
  font-weight: bold;
}
#content .doxygen a.qindexHL {
  font-weight: bold;
  background-color: #9CAFD4;
  color: #ffffff;
  border: 1px double #869DCA;
}
#content .doxygen .contents a.qindexHL:visited {
  color: #ffffff;
}
#content .doxygen a.el {
  font-weight: bold;
}
#content .doxygen a.code,
#content .doxygen a.code:visited {
  color: #4665A2;
}
#content .doxygen a.codeRef,
#content .doxygen a.codeRef:visited {
  color: #4665A2;
}
#content .doxygen dl.el {
  margin-left: -1cm;
}
#content .doxygen pre.fragment {
  border: 1px solid #C4CFE5;
  background-color: #FBFCFD;
  padding: 6px 6px;
  margin: 0px 8px 0px 2px;
  overflow: auto;
  word-wrap: break-word;
  font-size: 9pt;
  line-height: 125%;
  font-family: monospace, fixed;
  font-size: 105%;
}
#content .doxygen div.fragment {
  padding: 4px;
  margin: 4px 0px 4px 0px ;
  background-color: #FBFCFD;
  border: 1px solid #C4CFE5;
}
#content .doxygen div.line {
  font-family: monospace, fixed;
  font-size: 13px;
  min-height: 13px;
  line-height: 1.5 ;
  text-wrap: unrestricted;
  white-space: -moz-pre-wrap;
  /* Moz */

  white-space: -pre-wrap;
  /* Opera 4-6 */

  white-space: -o-pre-wrap;
  /* Opera 7 */

  white-space: pre-wrap;
  /* CSS3  */

  word-wrap: break-word;
  /* IE 5.5+ */

  text-indent: -53px;
  padding-left: 53px;
  padding-bottom: 0px;
  margin: 0px;
  -webkit-transition-property: background-color, box-shadow;
  -webkit-transition-duration: 0.5s;
  -moz-transition-property: background-color, box-shadow;
  -moz-transition-duration: 0.5s;
  -ms-transition-property: background-color, box-shadow;
  -ms-transition-duration: 0.5s;
  -o-transition-property: background-color, box-shadow;
  -o-transition-duration: 0.5s;
  transition-property: background-color, box-shadow;
  transition-duration: 0.5s;
}
#content .doxygen div.line.glow {
  background-color: cyan;
  box-shadow: 0 0 10px cyan;
}
#content .doxygen span.lineno {
  padding-right: 4px;
  text-align: right;
  border-right: 2px solid #0F0;
  background-color: #E8E8E8;
  white-space: pre;
}
#content .doxygen span.lineno a {
  background-color: #D8D8D8;
}
#content .doxygen span.lineno a:hover {
  background-color: #C8C8C8;
}
#content .doxygen div.ah {
  background-color: black;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 3px;
  margin-top: 3px;
  padding: 0.2em;
  border: solid thin #333;
  border-radius: 0.5em;
  -webkit-border-radius: .5em;
  -moz-border-radius: .5em;
  box-shadow: 2px 2px 3px #999;
  -webkit-box-shadow: 2px 2px 3px #999;
  -moz-box-shadow: rgba(0, 0, 0, 0.15) 2px 2px 2px;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#eeeeee), to(#000000), color-stop(0.3, #444444));
  background-image: -moz-linear-gradient(center top, #eeeeee 0%, #444444 40%, #000000);
}
#content .doxygen div.groupHeader {
  margin-left: 16px;
  margin-top: 12px;
  font-weight: bold;
}
#content .doxygen div.groupText {
  margin-left: 16px;
  font-style: italic;
}
#content .doxygen body {
  background-color: white;
  color: black;
  margin: 0;
}
#content .doxygen div.contents {
  margin-top: 10px;
  margin-left: 0px;
  margin-right: 0px;
}
#content .doxygen td.indexkey {
  background-color: #EBEFF6;
  font-weight: bold;
  border: 1px solid #C4CFE5;
  margin: 2px 0px 2px 0;
  padding: 2px 10px;
  white-space: nowrap;
  vertical-align: top;
}
#content .doxygen td.indexvalue {
  background-color: #EBEFF6;
  border: 1px solid #C4CFE5;
  padding: 2px 10px;
  margin: 2px 0px;
}
#content .doxygen tr.memlist {
  background-color: #EEF1F7;
}
#content .doxygen p.formulaDsp {
  text-align: center;
}
#content .doxygen img.formulaInl {
  vertical-align: middle;
}
#content .doxygen div.center {
  text-align: center;
  margin-top: 0px;
  margin-bottom: 0px;
  padding: 0px;
}
#content .doxygen div.center img {
  border: 0px;
}
#content .doxygen address.footer {
  text-align: right;
  padding-right: 12px;
}
#content .doxygen img.footer {
  border: 0px;
  vertical-align: middle;
}
#content .doxygen span.keyword {
  color: #008000;
}
#content .doxygen span.keywordtype {
  color: #604020;
}
#content .doxygen span.keywordflow {
  color: #e08000;
}
#content .doxygen span.comment {
  color: #800000;
}
#content .doxygen span.preprocessor {
  color: #806020;
}
#content .doxygen span.stringliteral {
  color: #002080;
}
#content .doxygen span.charliteral {
  color: #008080;
}
#content .doxygen span.vhdldigit {
  color: #ff00ff;
}
#content .doxygen span.vhdlchar {
  color: #000000;
}
#content .doxygen span.vhdlkeyword {
  color: #700070;
}
#content .doxygen span.vhdllogic {
  color: #ff0000;
}
#content .doxygen blockquote {
  background-color: #F7F8FB;
  border-left: 2px solid #9CAFD4;
  margin: 0 24px 0 4px;
  padding: 0 12px 0 16px;
}
#content .doxygen td.tiny {
  font-size: 75%;
}
#content .doxygen .dirtab {
  padding: 4px;
  border-collapse: collapse;
  border: 1px solid #A3B4D7;
}
#content .doxygen th.dirtab {
  background: #EBEFF6;
  font-weight: bold;
}
#content .doxygen hr {
  height: 0px;
  border: none;
  border-top: 1px solid #4A6AAA;
}
#content .doxygen hr.footer {
  height: 1px;
}
#content .doxygen table.memberdecls {
  border-spacing: 0px;
  padding: 0px;
}
#content .doxygen .memberdecls td {
  -webkit-transition-property: background-color, box-shadow;
  -webkit-transition-duration: 0.5s;
  -moz-transition-property: background-color, box-shadow;
  -moz-transition-duration: 0.5s;
  -ms-transition-property: background-color, box-shadow;
  -ms-transition-duration: 0.5s;
  -o-transition-property: background-color, box-shadow;
  -o-transition-duration: 0.5s;
  transition-property: background-color, box-shadow;
  transition-duration: 0.5s;
}
#content .doxygen .memberdecls td.glow {
  background-color: cyan;
  box-shadow: 0 0 15px cyan;
}
#content .doxygen .mdescLeft,
#content .doxygen .mdescRight,
#content .doxygen .memItemLeft,
#content .doxygen .memItemRight,
#content .doxygen .memTemplItemLeft,
#content .doxygen .memTemplItemRight,
#content .doxygen .memTemplParams {
  background-color: #F9FAFC;
  border: none;
  margin: 4px;
  padding: 1px 0 0 8px;
}
#content .doxygen .mdescLeft,
#content .doxygen .mdescRight {
  padding: 0px 8px 4px 8px;
  color: #555;
}
#content .doxygen .memItemLeft,
#content .doxygen .memItemRight,
#content .doxygen .memTemplParams {
  border-top: 1px solid #C4CFE5;
}
#content .doxygen .memItemLeft,
#content .doxygen .memTemplItemLeft {
  white-space: nowrap;
}
#content .doxygen .memItemRight {
  width: 100%;
}
#content .doxygen .memTemplParams {
  color: #4665A2;
  white-space: nowrap;
}
#content .doxygen .memtemplate {
  font-size: 80%;
  color: #4665A2;
  font-weight: normal;
  margin-left: 9px;
}
#content .doxygen .memnav {
  background-color: #EBEFF6;
  border: 1px solid #A3B4D7;
  text-align: center;
  margin: 2px;
  margin-right: 15px;
  padding: 2px;
}
#content .doxygen .mempage {
  width: 100%;
}
#content .doxygen .memitem {
  padding: 0;
  margin-bottom: 10px;
  margin-right: 5px;
  -webkit-transition: box-shadow 0.5s linear;
  -moz-transition: box-shadow 0.5s linear;
  -ms-transition: box-shadow 0.5s linear;
  -o-transition: box-shadow 0.5s linear;
  transition: box-shadow 0.5s linear;
  display: table !important;
  width: 100%;
}
#content .doxygen .memitem.glow {
  box-shadow: 0 0 15px cyan;
}
#content .doxygen .memname {
  font-weight: bold;
  margin-left: 6px;
}
#content .doxygen .memname td {
  vertical-align: bottom;
}
#content .doxygen .memproto,
#content .doxygen dl.reflist dt {
  border-top: 1px solid #A8B8D9;
  border-left: 1px solid #A8B8D9;
  border-right: 1px solid #A8B8D9;
  padding: 6px 0px 6px 0px;
  color: #253555;
  font-weight: bold;
  text-shadow: 0px 1px 1px rgba(255, 255, 255, 0.9);
  background-image: url('nav_f.png');
  background-repeat: repeat-x;
  background-color: #E2E8F2;
  /* opera specific markup */

  box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.15);
  border-top-right-radius: 4px;
  border-top-left-radius: 4px;
  /* firefox specific markup */

  -moz-box-shadow: rgba(0, 0, 0, 0.15) 5px 5px 5px;
  -moz-border-radius-topright: 4px;
  -moz-border-radius-topleft: 4px;
  /* webkit specific markup */

  -webkit-box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.15);
  -webkit-border-top-right-radius: 4px;
  -webkit-border-top-left-radius: 4px;
}
#content .doxygen .memdoc,
#content .doxygen dl.reflist dd {
  border-bottom: 1px solid #A8B8D9;
  border-left: 1px solid #A8B8D9;
  border-right: 1px solid #A8B8D9;
  padding: 6px 10px 2px 10px;
  background-color: #FBFCFD;
  border-top-width: 0;
  background-image: url('nav_g.png');
  background-repeat: repeat-x;
  background-color: #FFFFFF;
  /* opera specific markup */

  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.15);
  /* firefox specific markup */

  -moz-border-radius-bottomleft: 4px;
  -moz-border-radius-bottomright: 4px;
  -moz-box-shadow: rgba(0, 0, 0, 0.15) 5px 5px 5px;
  /* webkit specific markup */

  -webkit-border-bottom-left-radius: 4px;
  -webkit-border-bottom-right-radius: 4px;
  -webkit-box-shadow: 5px 5px 5px rgba(0, 0, 0, 0.15);
}
#content .doxygen dl.reflist dt {
  padding: 5px;
}
#content .doxygen dl.reflist dd {
  margin: 0px 0px 10px 0px;
  padding: 5px;
}
#content .doxygen .paramkey {
  text-align: right;
}
#content .doxygen .paramtype {
  white-space: nowrap;
}
#content .doxygen .paramname {
  color: #602020;
  white-space: nowrap;
}
#content .doxygen .paramname em {
  font-style: normal;
}
#content .doxygen .paramname code {
  line-height: 14px;
}
#content .doxygen .params,
#content .doxygen .retval,
#content .doxygen .exception,
#content .doxygen .tparams {
  margin-left: 0px;
  padding-left: 0px;
}
#content .doxygen .params .paramname,
#content .doxygen .retval .paramname {
  font-weight: bold;
  vertical-align: top;
}
#content .doxygen .params .paramtype {
  font-style: italic;
  vertical-align: top;
}
#content .doxygen .params .paramdir {
  font-family: "courier new", courier, monospace;
  vertical-align: top;
}
#content .doxygen table.mlabels {
  border-spacing: 0px;
}
#content .doxygen td.mlabels-left {
  width: 100%;
  padding: 0px;
}
#content .doxygen td.mlabels-right {
  vertical-align: bottom;
  padding: 0px;
  white-space: nowrap;
}
#content .doxygen span.mlabels {
  margin-left: 8px;
}
#content .doxygen span.mlabel {
  background-color: #728DC1;
  border-top: 1px solid #5373B4;
  border-left: 1px solid #5373B4;
  border-right: 1px solid #C4CFE5;
  border-bottom: 1px solid #C4CFE5;
  text-shadow: none;
  color: white;
  margin-right: 4px;
  padding: 2px 3px;
  border-radius: 3px;
  font-size: 7pt;
  white-space: nowrap;
}
#content .doxygen div.directory {
  margin: 10px 0px;
  border-top: 1px solid #A8B8D9;
  border-bottom: 1px solid #A8B8D9;
  width: 100%;
}
#content .doxygen .directory table {
  border-collapse: collapse;
}
#content .doxygen .directory td {
  margin: 0px;
  padding: 0px;
  vertical-align: top;
}
#content .doxygen .directory td.entry {
  white-space: nowrap;
  padding-right: 6px;
}
#content .doxygen .directory td.entry a {
  outline: none;
}
#content .doxygen .directory td.entry a img {
  border: none;
}
#content .doxygen .directory td.desc {
  width: 100%;
  padding-left: 6px;
  padding-right: 6px;
  border-left: 1px solid rgba(0, 0, 0, 0.05);
}
#content .doxygen .directory tr.even {
  padding-left: 6px;
  background-color: #F7F8FB;
}
#content .doxygen .directory img {
  vertical-align: -30%;
}
#content .doxygen .directory .levels {
  white-space: nowrap;
  width: 100%;
  text-align: right;
  font-size: 9pt;
}
#content .doxygen .directory .levels span {
  cursor: pointer;
  padding-left: 2px;
  padding-right: 2px;
  color: #3D578C;
}
#content .doxygen div.dynheader {
  margin-top: 8px;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
#content .doxygen address {
  font-style: normal;
  color: #2A3D61;
}
#content .doxygen table.doxtable {
  border-collapse: collapse;
  margin-top: 4px;
  margin-bottom: 4px;
}
#content .doxygen table.doxtable td,
#content .doxygen table.doxtable th {
  border: 1px solid #2D4068;
  padding: 3px 7px 2px;
}
#content .doxygen table.doxtable th {
  background-color: #374F7F;
  color: #FFFFFF;
  font-size: 110%;
  padding-bottom: 4px;
  padding-top: 5px;
}
#content .doxygen table.fieldtable {
  width: 100%;
  margin-bottom: 10px;
  border: 1px solid #A8B8D9;
  border-spacing: 0px;
  -moz-border-radius: 4px;
  -webkit-border-radius: 4px;
  border-radius: 4px;
  -moz-box-shadow: rgba(0, 0, 0, 0.15) 2px 2px 2px;
  -webkit-box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.15);
  box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.15);
}
#content .doxygen .fieldtable td,
#content .doxygen .fieldtable th {
  padding: 3px 7px 2px;
}
#content .doxygen .fieldtable td.fieldtype,
#content .doxygen .fieldtable td.fieldname {
  white-space: nowrap;
  border-right: 1px solid #A8B8D9;
  border-bottom: 1px solid #A8B8D9;
  vertical-align: top;
}
#content .doxygen .fieldtable td.fielddoc {
  border-bottom: 1px solid #A8B8D9;
  width: 100%;
}
#content .doxygen .fieldtable tr:last-child td {
  border-bottom: none;
}
#content .doxygen .fieldtable th {
  background-image: url('nav_f.png');
  background-repeat: repeat-x;
  background-color: #E2E8F2;
  font-size: 90%;
  color: #253555;
  padding-bottom: 4px;
  padding-top: 5px;
  text-align: left;
  -moz-border-radius-topleft: 4px;
  -moz-border-radius-topright: 4px;
  -webkit-border-top-left-radius: 4px;
  -webkit-border-top-right-radius: 4px;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  border-bottom: 1px solid #A8B8D9;
}
#content .doxygen .tabsearch {
  top: 0px;
  left: 10px;
  height: 36px;
  background-image: url('tab_b.png');
  z-index: 101;
  overflow: hidden;
  font-size: 13px;
}
#content .doxygen .navpath ul {
  font-size: 11px;
  background-image: url('tab_b.png');
  background-repeat: repeat-x;
  height: 30px;
  line-height: 30px;
  color: #8AA0CC;
  border: solid 1px #C2CDE4;
  overflow: hidden;
  margin: 0px;
  padding: 0px;
}
#content .doxygen .navpath li {
  list-style-type: none;
  float: left;
  padding-left: 10px;
  padding-right: 15px;
  background-image: url('bc_s.png');
  background-repeat: no-repeat;
  background-position: right;
  color: #364D7C;
}
#content .doxygen .navpath li.navelem a {
  height: 32px;
  display: block;
  text-decoration: none;
  outline: none;
}
#content .doxygen .navpath li.navelem a:hover {
  color: #6884BD;
}
#content .doxygen .navpath li.footer {
  list-style-type: none;
  float: right;
  padding-left: 10px;
  padding-right: 15px;
  background-image: none;
  background-repeat: no-repeat;
  background-position: right;
  color: #364D7C;
  font-size: 8pt;
}
#content .doxygen div.summary {
  float: right;
  font-size: 8pt;
  padding-right: 5px;
  width: 50%;
  text-align: right;
}
#content .doxygen div.summary a {
  white-space: nowrap;
}
#content .doxygen div.ingroups {
  font-size: 8pt;
  width: 50%;
  text-align: left;
}
#content .doxygen div.ingroups a {
  white-space: nowrap;
}
#content .doxygen div.header {
  background-image: url('nav_h.png');
  background-repeat: repeat-x;
  background-color: #F9FAFC;
  margin: 0px;
  border-bottom: 1px solid #C4CFE5;
}
#content .doxygen div.headertitle {
  padding: 5px 5px 5px 7px;
}
#content .doxygen dl {
  padding: 0 0 0 10px;
}
#content .doxygen dl.section {
  margin-left: 0px;
  padding-left: 0px;
}
#content .doxygen dl.note {
  margin-left: -7px;
  padding-left: 3px;
  border-left: 4px solid;
  border-color: #D0C000;
}
#content .doxygen dl.warning,
#content .doxygen dl.attention {
  margin-left: -7px;
  padding-left: 3px;
  border-left: 4px solid;
  border-color: #FF0000;
}
#content .doxygen dl.pre,
#content .doxygen dl.post,
#content .doxygen dl.invariant {
  margin-left: -7px;
  padding-left: 3px;
  border-left: 4px solid;
  border-color: #00D000;
}
#content .doxygen dl.deprecated {
  margin-left: -7px;
  padding-left: 3px;
  border-left: 4px solid;
  border-color: #505050;
}
#content .doxygen dl.todo {
  margin-left: -7px;
  padding-left: 3px;
  border-left: 4px solid;
  border-color: #00C0E0;
}
#content .doxygen dl.test {
  margin-left: -7px;
  padding-left: 3px;
  border-left: 4px solid;
  border-color: #3030E0;
}
#content .doxygen dl.bug {
  margin-left: -7px;
  padding-left: 3px;
  border-left: 4px solid;
  border-color: #C08050;
}
#content .doxygen dl.section dd {
  margin-bottom: 6px;
}
#content .doxygen #projectlogo {
  text-align: center;
  vertical-align: bottom;
  border-collapse: separate;
}
#content .doxygen #projectlogo img {
  border: 0px none;
}
#content .doxygen #projectname {
  font: 300% Tahoma, Arial, sans-serif;
  margin: 0px;
  padding: 2px 0px;
}
#content .doxygen #projectbrief {
  font: 120% Tahoma, Arial, sans-serif;
  margin: 0px;
  padding: 0px;
}
#content .doxygen #projectnumber {
  font: 50% Tahoma, Arial, sans-serif;
  margin: 0px;
  padding: 0px;
}
#content .doxygen #titlearea {
  padding: 0px;
  margin: 0px;
  width: 100%;
  border-bottom: 1px solid #5373B4;
}
#content .doxygen .image {
  text-align: center;
}
#content .doxygen .dotgraph {
  text-align: center;
}
#content .doxygen .mscgraph {
  text-align: center;
}
#content .doxygen .caption {
  font-weight: bold;
}
#content .doxygen div.zoom {
  border: 1px solid #90A5CE;
}
#content .doxygen dl.citelist {
  margin-bottom: 50px;
}
#content .doxygen dl.citelist dt {
  color: #334975;
  float: left;
  font-weight: bold;
  margin-right: 10px;
  padding: 5px;
}
#content .doxygen dl.citelist dd {
  margin: 2px 0;
  padding: 5px 0;
}
#content .doxygen div.toc {
  padding: 14px 25px;
  background-color: #F4F6FA;
  border: 1px solid #D8DFEE;
  border-radius: 3px 3px 3px 3px;
  float: right;
  height: auto;
  margin: 0px 0px 10px 10px;
  width: 200px;
}
#content .doxygen div.toc li {
  background: url("bdwn.png") no-repeat scroll 0 5px transparent;
  font: 10px/1.2 Verdana, DejaVu Sans, Geneva, sans-serif;
  margin-top: 5px;
  padding-left: 10px;
  padding-top: 2px;
}
#content .doxygen div.toc h3 {
  font: bold 12px/1.2 Arial, FreeSans, sans-serif;
  color: #4665A2;
  border-bottom: 0 none;
  margin: 0;
}
#content .doxygen div.toc ul {
  list-style: none outside none;
  border: medium none;
  padding: 0px;
}
#content .doxygen div.toc li.level1 {
  margin-left: 0px;
}
#content .doxygen div.toc li.level2 {
  margin-left: 15px;
}
#content .doxygen div.toc li.level3 {
  margin-left: 30px;
}
#content .doxygen div.toc li.level4 {
  margin-left: 45px;
}
#content .doxygen .inherit_header {
  font-weight: bold;
  color: gray;
  cursor: pointer;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
#content .doxygen .inherit_header td {
  padding: 6px 0px 2px 5px;
}
#content .doxygen .inherit {
  display: none;
}
#content .doxygen tr.heading h2 {
  margin-top: 12px;
  margin-bottom: 4px;
}
@media print {
  #content .doxygen #top {
    display: none;
  }
  #content .doxygen #side-nav {
    display: none;
  }
  #content .doxygen #nav-path {
    display: none;
  }
  #content .doxygen body {
    overflow: visible;
  }
  #content .doxygen h1,
  #content .doxygen h2,
  #content .doxygen h3,
  #content .doxygen h4,
  #content .doxygen h5,
  #content .doxygen h6 {
    page-break-after: avoid;
  }
  #content .doxygen .summary {
    display: none;
  }
  #content .doxygen .memitem {
    page-break-inside: avoid;
  }
  #content .doxygen #doc-content {
    margin-left: 0 !important;
    height: auto !important;
    width: auto !important;
    overflow: inherit;
    display: inline;
  }
}

/* missing in original doxygen.css ? */
#content .doxygen div.tabs,
#content .doxygen div.tabs2,
#content .doxygen div.tabs3,
#content .doxygen div.tabs4 {
    padding: 0 ;
    margin: 0 ;
    border: 1px solid white ;
    font-size: .9em ;
}
#content .doxygen ul.tablist {
    display: block ;
    width: 100% ;
    margin: 5px 0 ;
    padding: 0 ;
    border-bottom: 2px solid #aaa ;
}
#content .doxygen ul.tablist li {
    display: inline-block ;
    margin: 0px 4px ;
    padding: 5px ;
    border: 1px solid #aaa ;
    border-bottom: none ;
    background-color: #f6f6f6 ;
}
#content .doxygen ul.tablist li.current {
    background-color: inherit ;
}
#content .doxygen ul.tablist li a {
    text-decoration : none ;
    color: black ;
}
#content .doxygen ul.tablist li.current a {
    font-weight: bold ;
}
