@inproceedings{arthur_kmeanspp_2007,
    title = {k-means++: the advantages of careful seeding},
    booktitle = {Proceedings of the eighteenth annual ACM-SIAM symposium on Discrete algorithms},
    publisher = {Society for Industrial and Applied Mathematics Philadelphia, PA, USA},
    author = {<PERSON><PERSON> and <PERSON><PERSON>},
    year = {2007},
    pages = {1027--1035}
},



@inproceedings{winder_learning_2007,
	title = {Learning Local Image Descriptors},
	doi = {10.1109/CVPR.2007.382971},
	abstract = {In this paper we study interest point descriptors for image matching and 3D reconstruction. We examine the building blocks of descriptor algorithms and evaluate numerous combinations of components. Various published descriptors such as SIFT, GLOH, and Spin images can be cast into our framework. For each candidate algorithm we learn good choices for parameters using a training set consisting of patches from a multi-image 3D reconstruction where accurate ground-truth matches are known. The best descriptors were those with log polar histogramming regions and feature vectors constructed from rectified outputs of steerable quadrature filters. At a 95\% detection rate these gave one third of the incorrect matches produced by SIFT.},
	booktitle = {CVPR},
	author = {<PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>},
	year = {2007},
	keywords = {3D image reconstruction,feature vectors,GLOH images,image reconstruction,local image descriptors,log polar histogramming,SIFT images,Spin images,steerable quadrature filters,vectors},
	pages = {1-8}
},

@article{arya_optimal_1998,
	title = {An optimal algorithm for approximate nearest neighbor searching in fixed dimensions},
	volume = {45},
	url = {citeseer.ist.psu.edu/article/arya94optimal.html},
	journal = {Journal of the ACM},
	author = {Sunil Arya and David M. Mount and Nathan S. Netanyahu and Ruth Silverman and Angela Y. Wu},
	year = {1998},
	pages = {891-923}
},

@article{lowe_sift_2004,
	title = {Distinctive image features from scale-invariant keypoints},
	volume = {60},
	journal = {Int. Journal of Computer Vision},
	author = {David G. Lowe},
	year = {2004},
	pages = {91-110}
},

@inproceedings{liu_efficient_2003,
	title = {Efficient Exact k-NN and Nonparametric Classification in High Dimensions},
	booktitle = {Neural Information Processing Systems},
	author = {T. Liu and A. W. Moore and A. Gray},
	year = {2003}
},

@inproceedings{nister_scalable_2006,
	title = {Scalable Recognition with a Vocabulary Tree},
	isbn = {0-7695-2597-0},
	doi = {http://dx.doi.org/10.1109/CVPR.2006.264},
	booktitle = {CVPR},
	author = {David Nister and Henrik Stewenius},
	year = {2006},
	pages = {2161-2168}
},

@inproceedings{beis_shape_1997,
	title = {Shape indexing using approximate nearest-neighbor search in high dimensional spaces},
	url = {citeseer.ist.psu.edu/beis97shape.html},
	booktitle = {CVPR},
	author = {Jeffrey S. Beis and David G. Lowe},
	year = {1997},
	pages = {1000-1006}
},

@inproceedings{leibe_efficient_2006,
	title = {Efficient Clustering and Matching for Object Class Recognition},
	url = {http://www.mis.informatik.tu-darmstadt.de/Publications/leibe-efficientclustering-bmvc06.pdf},
	abstract = {In this paper we address the problem of building object class representations based on local features and fast matching in a large database. We propose an efficient algorithm for hierarchical agglomerative clustering. We examine different agglomerative and partitional clustering strategies and compare the quality of obtained clusters. Our combination of partitional-agglomerative clustering gives significant improvement in terms of efficiency while maintaining the same quality of clusters. We also propose a method for building data structures for fast matching in high dimensional feature spaces. These improvements allow to deal with large sets of training data typically used in recognition of multiple object classes.},
    booktitle = {BMVC},
	author = {B. Leibe and K. Mikolajczyk and B. Schiele},
	year = {2006}
},

@inproceedings{schindler_city-scale_2007,
	title = {City-Scale Location Recognition},
	doi = {10.1109/CVPR.2007.383150},
	abstract = {We look at the problem of location recognition in a large image dataset using a vocabulary tree. This entails finding the location of a query image in a large dataset containing 3times104 streetside images of a city. We investigate how the traditional invariant feature matching approach falls down as the size of the database grows. In particular we show that by carefully selecting the vocabulary using the most informative features, retrieval performance is significantly improved, allowing us to increase the number of database images by a factor of 10. We also introduce a generalization of the traditional vocabulary tree search algorithm which improves performance by effectively increasing the branching factor of a fixed vocabulary tree.},
    booktitle = {CVPR},
	journal = {Computer Vision and Pattern Recognition, 2007. CVPR '07. IEEE Conference on},
	author = {G. Schindler and M. Brown and R. Szeliski},
	year = {2007},
    keywords = {feature matching,image matching,image retrieval,query image,trees (mathematics)city-scale location recognition,vocabulary tree},
	pages = {1-7}
},

@article{freidman_algorithm_1977,
	title = {An Algorithm for Finding Best Matches in Logarithmic Expected Time},
	volume = {3},
	issn = {0098-3500},
	doi = {http://doi.acm.org/10.1145/355744.355745},
	journal = {ACM Trans. Math. Softw.},
	author = {Jerome H. Freidman and Jon Louis Bentley and Raphael Ari Finkel},
	year = {1977},
	pages = {209--226}
},

@inproceedings{brin_near_1995,
	title = {Near Neighbor Search in Large Metric Spaces},
	isbn = {1-55860-379-4},
	booktitle = {VLDB},
	author = {Sergey Brin},
	year = {1995},
	pages = {574-584}
},

@inproceedings{liu_investigation_2004,
	title = {An investigation of practical approximate nearest neighbor algorithms},
	url = {citeseer.ist.psu.edu/753047.html},
	booktitle = {Neural Information Processing Systems},
	author = {T. Liu and A. Moore and A. Gray and K. Yang},
	year = {2004}
},

@article{snavely_photo_2006,
	title = {Photo tourism: Exploring photo collections in 3{D}},
	volume = {25},
	journal = {ACM Transactions on Graphics (TOG)},
	author = {N. Snavely and S. M. Seitz and R. Szeliski},
	year = {2006},
	pages = {835-846}
},

@inproceedings{silpa-anan_localization_2004,
	title = {Localization using an imagemap},
	booktitle = {Australasian Conference on Robotics and Automation},
	author = {C. Silpa-Anan and R. Hartley},
	year = {2004}
},

@inproceedings{sivic_videogoogle_2003,
	title = {Video {G}oogle: A Text Retrieval Approach to Object Matching in Videos},
	booktitle = {ICCV},
	author = {J. Sivic and A. Zisserman},
	year = {2003}
},

@techreport{torralba_tiny_2007,
        Author = {A. Torralba and R. Fergus and W. T. Freeman},
        Title = {Tiny Images},
        Institution = {CSAIL, Massachusetts Institute of Technology},
        Year = {2007},
        URL = {http://dspace.mit.edu/handle/1721.1/37291},
        Number = {MIT-CSAIL-TR-2007-024}
},


@article{torralba_80_million_2008,
    author = {Antonio Torralba and Rob Fergus and William T. Freeman},
    title = {80 Million Tiny Images: A Large Data Set for Nonparametric Object and Scene Recognition},
    journal ={IEEE Transactions on Pattern Analysis and Machine Intelligence},
    volume = {30},
    number = {11},
    issn = {0162-8828},
    year = {2008},
    pages = {1958-1970},
    doi = {http://doi.ieeecomputersociety.org/10.1109/TPAMI.2008.128},
    publisher = {IEEE Computer Society},
    address = {Los Alamitos, CA, USA},
}

@inproceedings{philbin_oxford_2007,
	title = {Object retrieval with large vocabularies and fast spatial matching},
	booktitle = {CVPR},
	author = {J. Philbin and O. Chum and M. Isard and J. Sivic and A. Zisserman},
	year = {2007}
}

@article{andoni_near-optimal_2006,
    title = {Near-Optimal Hashing Algorithms for Approximate Nearest Neighbor in High Dimensions},
    journal = {Proceedings of the 47th Annual IEEE Symposium on Foundations of Computer Science (FOCS'06)},
    author = {A. Andoni},
    year = {2006},
    pages = {459-468}
}

@article{fukunaga_branch_1975,
    title = {A Branch and Bound Algorithm for Computing k-Nearest Neighbors},
    volume = {24},
    url = {http://portal.acm.org/citation.cfm?id=1311063.1311121\&coll=GUIDE\&dl=\&CFID=5674080\&CFTOKEN=11648065},
    abstract = {Computation of the k-nearest neighbors generally requires a large number of expensive distance computations. The method of branch and bound is implemented in the present algorithm to facilitate rapid calculation of the k-nearest neighbors, by eliminating the necesssity of calculating many distances. Experimental results demonstrate the efficiency of the algorithm. Typically, an average of only 61 distance computations were made to find the nearest neighbor of a test sample among 1000 design samples.},
    journal = {IEEE Trans. Comput.},
    author = {K. Fukunaga and P. M. Narendra},
    year = {1975},
    keywords = {branch and bound,distance computation,hierarchical decomposition,k-nearest neighbors,tree-search algorithm.},
    pages = {750-753}
}


@inproceedings{mikolajczyk_improving_2007,
    title = {Improving Descriptors for Fast Tree Matching by Optimal Linear Projection},
    isbn = {1550-5499},
    doi = {10.1109/ICCV.2007.4408871},
    abstract = {In this paper we propose to transform an image descriptor so that nearest neighbor (NN) search for correspondences becomes the optimal matching strategy under the assumption that inter-image deviations of corresponding descriptors have Gaussian distribution. The Euclidean NN in the transformed domain corresponds to the NN according to a truncated Mahalanobis metric in the original descriptor space. We provide theoretical justification for the proposed approach and show experimentally that the transformation allows a significant dimensionality reduction and improves matching performance of a state-of-the art SIFT descriptor. We observe consistent improvement in precision-recall and speed of fast matching in tree structures at the expense of little overhead for projecting the descriptors into transformed space. In the context of SIFT vs. transformed M-SIFT comparison, tree search structures are evaluated according to different criteria and query types. All search tree experiments confirm that transformed M-SIFT performs better than the original SIFT.},
    booktitle = {Computer Vision, 2007. ICCV 2007. IEEE 11th International Conference on},
    journal = {Computer Vision, 2007. ICCV 2007. IEEE 11th International Conference on},
    author = {Krystian Mikolajczyk and Jiri Matas},
    year = {2007},
    pages = {1-8}
}

@inproceedings{silpa-anan_optimized_2008,
     author = {Silpa-Anan, C. and Hartley, R.} ,
     title = "Optimised {KD}-trees for fast image descriptor matching" ,
     booktitle = {CVPR} ,
     year = 2008 ,
     url = "../Papers/PDF/SilpaAnan:CVPR08.pdf" ,
};