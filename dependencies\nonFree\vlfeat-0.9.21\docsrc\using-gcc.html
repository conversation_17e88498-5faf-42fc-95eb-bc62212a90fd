<!DOCTYPE group PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<group>

<p>These instructions show how to setup a basic C++ project which uses the
<b>VLFeat</b> library and the g++ compiler. Our project will consist
of the following simple file, which we will refer to
as <code>main.cpp</code>:</p>

<precode type="c">
extern "C" {
  #include &lt;vl/generic.h&gt;
}

int main (int argc, const char * argv[]) {
  VL_PRINT ("Hello world!") ;
  return 0;
}
</precode>

 <p>Notice that the <code>extern "C"</code> is required for C++ projects.
 Compiling the project is straightforward. From the command prompt type:</p>

<precode type="sh">
$ g++ main.cpp -o vlfeat-test -IVLROOT -LVLROOT/bin/a64/ -lvl
</precode>

 <p>In this example, replace <code>VLROOT</code> with the path to your VLFeat
 installation and replace <code>a64</code> with your architecture
 (<code>a64</code> for 64 bit linux, <code>glx</code> for 32 bit linux).</p>

</group>
