#include "IStitch_gpu.hpp"
#include <algorithm>
#include <io.hpp>
#include <photocap.hpp>

namespace SWDC {
	namespace stitch {
#undef min
#undef max
		IStitch_gpu::IStitch_gpu(const data::Data& data, const HashMap<stitch::ImagePair, std::string>& outputMap) :IStitch(data, outputMap)
		{
			std::vector<cl::Platform> platforms;
			cl::Platform::get(&platforms);

			for (auto& p : platforms) {
				std::string platver = p.getInfo<CL_PLATFORM_VERSION>();
				std::string name = p.getInfo<CL_PLATFORM_NAME>();
				if ((platver.find("OpenCL 2.") != std::string::npos || platver.find("OpenCL 3.") != std::string::npos) && name.find("NVIDIA CUDA") != std::string::npos)
				{
					_platform = p;
				}
			}

			if (_platform() == 0) {
				throw std::runtime_error("No OpenCL 2.0 or newer platform found.");
			}

			// 获取平台属性信息
			std::string platformName;
			_platform.getInfo(CL_PLATFORM_NAME, &platformName);

			std::string platformVersion;
			_platform.getInfo(CL_PLATFORM_VERSION, &platformVersion);

			// 打印平台信息
			std::cout << "Platform Name: " << platformName << std::endl;
			std::cout << "Platform Version: " << platformVersion << std::endl;

			_platform.getDevices(CL_DEVICE_TYPE_GPU, &_devices);

			if (_devices.size() == 0) {
				throw std::runtime_error("No device found.");
			}
		}

		void IStitch_gpu::genCorrectionImage(const image::Image<Vec3c>& image, const std::shared_ptr<camera::IntrinsicBase> camera, const data::Pose3& pose, image::Image<Vec3c>* imageout)
		{
			Quadrangle quadrangle = getGroundQuadrangle(camera, pose);

			double xmin = std::min({ quadrangle.leftTop(0), quadrangle.rightTop(0), quadrangle.rightBottom(0), quadrangle.leftBottom(0) });
			double xmax = std::max({ quadrangle.leftTop(0), quadrangle.rightTop(0), quadrangle.rightBottom(0), quadrangle.leftBottom(0) });

			double ymin = std::min({ quadrangle.leftTop(1), quadrangle.rightTop(1), quadrangle.rightBottom(1), quadrangle.leftBottom(1) });
			double ymax = std::max({ quadrangle.leftTop(1), quadrangle.rightTop(1), quadrangle.rightBottom(1), quadrangle.leftBottom(1) });

			int width = fabs(xmax - xmin) / _groundSampleSize;
			int height = fabs(ymax - ymin) / _groundSampleSize;

			imageout->resize(width, height, image.Channels());
			cl_int errNum = CL_SUCCESS;
			cl::Context context = cl::Context(_devices, NULL, NULL, NULL, &errNum);
			if (errNum != CL_SUCCESS) {
				throw std::runtime_error("Failed to create context.");
			}

			cl_queue_properties queueProperties[] = { CL_QUEUE_PROPERTIES, CL_QUEUE_PROFILING_ENABLE, 0 };
			cl::CommandQueue queue(context, _devices[0], queueProperties, &errNum);

			if (errNum != CL_SUCCESS) {
				throw std::runtime_error("Failed to create command queue.");
			}

			std::vector<float> center = { static_cast<float>(pose.x()) , static_cast<float>(pose.y()) , static_cast<float>(pose.z()) };
			std::vector<float> rotation =
			{ static_cast<float>(pose.getRotation()(0, 0)), static_cast<float>(pose.getRotation()(0, 1)), static_cast<float>(pose.getRotation()(0, 2)),
			  static_cast<float>(pose.getRotation()(1, 0)), static_cast<float>(pose.getRotation()(1, 1)), static_cast<float>(pose.getRotation()(1, 2)),
			  static_cast<float>(pose.getRotation()(2, 0)), static_cast<float>(pose.getRotation()(2, 1)), static_cast<float>(pose.getRotation()(2, 2))
			};

			unsigned char* imageDataPtr = const_cast<unsigned char*>(reinterpret_cast<const unsigned char*>(image.data()));
			cl::Buffer bufferSrcImage(context, CL_MEM_READ_ONLY | CL_MEM_COPY_HOST_PTR, sizeof(unsigned char) * image.Height() * image.Width() * image.Channels(), imageDataPtr);
			cl::Buffer bufferCenter(context, CL_MEM_READ_ONLY | CL_MEM_COPY_HOST_PTR, sizeof(float) * center.size(), center.data());
			cl::Buffer bufferRotation(context, CL_MEM_READ_ONLY | CL_MEM_COPY_HOST_PTR, sizeof(float) * rotation.size(), rotation.data());
			cl::Buffer bufferOutImage(context, CL_MEM_WRITE_ONLY, sizeof(unsigned char) * imageout->Height() * imageout->Width() * imageout->Channels());

			cl::Program program(context, cl::Program::Sources(1, std::make_pair(kernel::kernelSource, strlen(kernel::kernelSource))), &errNum);
			if (errNum != CL_SUCCESS) {
				throw std::runtime_error("Failed to create program.");
			}
			program.build(_devices);

			cl::Kernel kernel(program, "correctionImage", &errNum);
			if (errNum != CL_SUCCESS) {
				throw std::runtime_error("Failed to create kernel.");
			}

			kernel.setArg(0, camera->w());
			kernel.setArg(1, camera->h());
			kernel.setArg(2, image.Channels());
			kernel.setArg(3, static_cast<float>(camera->pixel()));
			kernel.setArg(4, static_cast<float>(camera->focal()));
			kernel.setArg(5, static_cast<float>(xmin));
			kernel.setArg(6, static_cast<float>(ymax));
			kernel.setArg(7, static_cast<float>(_averageHeight));
			kernel.setArg(8, width);
			kernel.setArg(9, height);
			kernel.setArg(10, static_cast<float>(_groundSampleSize));
			kernel.setArg(11, bufferCenter);
			kernel.setArg(12, bufferRotation);
			kernel.setArg(13, bufferSrcImage);
			kernel.setArg(14, bufferOutImage);

			queue.enqueueNDRangeKernel(kernel, cl::NullRange, cl::NDRange(height, width));
			queue.finish();

			queue.enqueueReadBuffer(bufferOutImage, CL_TRUE, 0, sizeof(unsigned char) * imageout->Width() * imageout->Height() * imageout->Channels(), imageout->data());

		}

		void IStitch_gpu::genOverlapRegionFeature(const image::Image<Vec3c>& image, const std::shared_ptr<camera::IntrinsicBase> camera, const data::Pose3& pose, const Quadrangle& overlapRectangle, const std::shared_ptr<feature::ImageDescriber> imageDescriber, std::unique_ptr<feature::Regions>& regions)
		{
			imageDescriber->allocate(regions);
			int overlapWidth = ceil(fabs(overlapRectangle.leftTop(0) - overlapRectangle.rightTop(0)) / _groundSampleSize);
			int overlapHeight = ceil(fabs(overlapRectangle.leftTop(1) - overlapRectangle.leftBottom(1)) / _groundSampleSize);

			int widthBlock = overlapWidth;
			int heightBlock = overlapHeight;

			bool widthCut = true;

			int cutNum = 4;

			if (overlapWidth > overlapHeight) {
				widthBlock = floor(overlapWidth / cutNum);
				widthCut = true;
			}
			else
			{
				heightBlock = floor(overlapHeight / cutNum);
				widthCut = false;
			}

			std::vector<float> center = { static_cast<float>(pose.x()) , static_cast<float>(pose.y()) , static_cast<float>(pose.z()) };
			std::vector<float> rotation =
			{ static_cast<float>(pose.getRotation()(0, 0)), static_cast<float>(pose.getRotation()(0, 1)), static_cast<float>(pose.getRotation()(0, 2)),
			  static_cast<float>(pose.getRotation()(1, 0)), static_cast<float>(pose.getRotation()(1, 1)), static_cast<float>(pose.getRotation()(1, 2)),
			  static_cast<float>(pose.getRotation()(2, 0)), static_cast<float>(pose.getRotation()(2, 1)), static_cast<float>(pose.getRotation()(2, 2))
			};
			unsigned char* imageDataPtr = const_cast<unsigned char*>(reinterpret_cast<const unsigned char*>(image.data()));

			cl_int errNum = CL_SUCCESS;
			cl::Context context = cl::Context(_devices, NULL, NULL, NULL, &errNum);
			if (errNum != CL_SUCCESS) {
				throw std::runtime_error("Failed to create context.");
			}

			cl_queue_properties queueProperties[] = { CL_QUEUE_PROPERTIES, CL_QUEUE_PROFILING_ENABLE, 0 };
			cl::CommandQueue queue(context, _devices[0], queueProperties, &errNum);

			if (errNum != CL_SUCCESS) {
				throw std::runtime_error("Failed to create command queue.");
			}

			cl::Program program(context, cl::Program::Sources(1, std::make_pair(kernel::kernelSource, strlen(kernel::kernelSource))), &errNum);
			if (errNum != CL_SUCCESS) {
				throw std::runtime_error("Failed to create program.");
			}
			program.build(_devices);

			cl::Kernel kernel(program, "correctionPartImage", &errNum);
			if (errNum != CL_SUCCESS) {
				throw std::runtime_error("Failed to create kernel.");
			}

			cl::Buffer bufferSrcImage(context, CL_MEM_READ_ONLY | CL_MEM_COPY_HOST_PTR, sizeof(unsigned char) * image.Height() * image.Width() * image.Channels(), imageDataPtr);
			cl::Buffer bufferCenter(context, CL_MEM_READ_ONLY | CL_MEM_COPY_HOST_PTR, sizeof(float) * center.size(), center.data());
			cl::Buffer bufferRotation(context, CL_MEM_READ_ONLY | CL_MEM_COPY_HOST_PTR, sizeof(float) * rotation.size(), rotation.data());

			kernel.setArg(0, camera->w());
			kernel.setArg(1, camera->h());
			kernel.setArg(2, image.Channels());
			kernel.setArg(3, static_cast<float>(camera->pixel()));
			kernel.setArg(4, static_cast<float>(camera->focal()));
			kernel.setArg(5, static_cast<float>(overlapRectangle.leftTop(0)));
			kernel.setArg(6, static_cast<float>(overlapRectangle.leftTop(1)));
			kernel.setArg(7, static_cast<float>(_averageHeight));
			kernel.setArg(8, widthBlock);
			kernel.setArg(9, heightBlock);
			kernel.setArg(10, static_cast<float>(_groundSampleSize));
			kernel.setArg(11, bufferCenter);
			kernel.setArg(12, bufferRotation);
			kernel.setArg(13, bufferSrcImage);

			for (int k = 0; k < cutNum; k++)
			{
				int heightOffset = 0, widthOffset = 0;
				if (widthCut)
				{
					widthOffset = widthBlock * k;
					heightOffset = 0;
				}
				else
				{
					widthOffset = 0;
					heightOffset = heightBlock * k;
				}

				image::Image<float> overlapImage;
				overlapImage.resize(widthBlock, heightBlock, 1);

				cl::Buffer bufferOutImage(context, CL_MEM_WRITE_ONLY, sizeof(float) * overlapImage.Height() * overlapImage.Width() * overlapImage.Channels());
				kernel.setArg(14, bufferOutImage);

				queue.enqueueNDRangeKernel(kernel, cl::NDRange(heightOffset, widthOffset), cl::NDRange(heightBlock, widthBlock));
				queue.finish();
				queue.enqueueReadBuffer(bufferOutImage, CL_TRUE, 0, sizeof(float) * overlapImage.Width() * overlapImage.Height() * overlapImage.Channels(), overlapImage.data());

				std::unique_ptr<feature::Regions> overlapImageRegions;
				imageDescriber->allocate(overlapImageRegions);
				imageDescriber->describe(overlapImage, overlapImageRegions);
				feature::PointFeatures& pointFeatures = overlapImageRegions->Features();

#pragma omp parallel for schedule(dynamic)
				for (int j = 0; j < pointFeatures.size(); j++)
				{
					if (widthCut) {
						pointFeatures.at(j).x() += k * widthBlock;
					}
					else
					{
						pointFeatures.at(j).y() += k * heightBlock;
					}
				}

				regions->AddRegion(overlapImageRegions.get());
			}
		}
	}
}
