indent_align_string=false
indent_braces=false
indent_braces_no_func=false
indent_brace_parent=false
indent_namespace=false
indent_extern=false
indent_class=true
indent_class_colon=false
indent_else_if=false
indent_func_call_param=false
indent_func_def_param=false
indent_func_proto_param=false
indent_func_class_param=false
indent_func_ctor_var_param=false
indent_template_param=false
indent_func_param_double=false
indent_relative_single_line_comments=true
indent_col1_comment=true
indent_access_spec_body=false
indent_paren_nl=false
indent_comma_paren=false
indent_bool_paren=false
indent_square_nl=false
indent_preserve_sql=false
indent_align_assign=true
sp_balance_nested_parens=false
align_keep_tabs=false
align_with_tabs=false
align_on_tabstop=false
align_number_left=false
align_func_params=false
align_same_func_call_params=false
align_var_def_colon=false
align_var_def_attribute=false
align_var_def_inline=false
align_right_cmt_mix=false
align_on_operator=false
align_mix_var_proto=false
align_single_line_func=false
align_single_line_brace=false
align_nl_cont=false
align_left_shift=true
nl_collapse_empty_body=false
nl_assign_leave_one_liners=true
nl_class_leave_one_liners=true
nl_enum_leave_one_liners=true
nl_getset_leave_one_liners=true
nl_func_leave_one_liners=true
nl_if_leave_one_liners=true
nl_multi_line_cond=false
nl_multi_line_define=false
nl_before_case=false
nl_after_case=false
nl_after_return=false
nl_after_semicolon=false
nl_after_brace_open=false
nl_after_brace_open_cmt=false
nl_after_vbrace_open=false
nl_after_brace_close=false
nl_define_macro=false
nl_squeeze_ifdef=false
nl_ds_struct_enum_cmt=false
nl_ds_struct_enum_close_brace=false
nl_create_if_one_liner=true
nl_create_for_one_liner=true
nl_create_while_one_liner=true
ls_for_split_full=false
ls_func_split_full=false
nl_after_multiline_comment=false
eat_blanks_after_open_brace=false
eat_blanks_before_close_brace=false
mod_pawn_semicolon=false
mod_full_paren_if_bool=true
mod_remove_extra_semicolon=true
mod_sort_import=false
mod_sort_using=false
mod_sort_include=false
mod_move_case_break=false
mod_remove_empty_return=false
cmt_indent_multi=true
cmt_c_group=false
cmt_c_nl_start=false
cmt_c_nl_end=false
cmt_cpp_group=false
cmt_cpp_nl_start=false
cmt_cpp_nl_end=false
cmt_cpp_to_c=false
cmt_star_cont=false
cmt_multi_check_last=true
cmt_insert_before_preproc=false
pp_indent_at_level=false
pp_region_indent_code=false
pp_if_indent_code=false
pp_define_at_level=false
input_tab_size=4
indent_columns=4
indent_with_tabs=0
sp_before_ptr_star=remove
sp_between_ptr_star=remove
sp_after_ptr_star=add
sp_before_byref=remove
sp_after_byref=add
sp_after_type=ignore
sp_else_brace=add
sp_catch_brace=add
sp_finally_brace=add
sp_try_brace=add
nl_end_of_file=add
nl_fcall_brace=remove
nl_enum_brace=add
nl_struct_brace=add
nl_union_brace=remove
nl_if_brace=remove
nl_brace_else=add
nl_else_brace=remove
nl_else_if=remove
nl_brace_finally=add
nl_finally_brace=remove
nl_try_brace=remove
nl_for_brace=remove
nl_catch_brace=remove
nl_brace_catch=add
nl_while_brace=remove
nl_do_brace=remove
nl_brace_while=remove
nl_switch_brace=remove
nl_namespace_brace=add
nl_template_class=add
nl_class_brace=add
nl_fdef_brace=add
mod_full_brace_function=add
mod_paren_on_return=remove
