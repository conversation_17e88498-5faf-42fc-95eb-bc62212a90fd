% VL_IRODR  Inverse Rod<PERSON><PERSON>' formula
%   OM = VL_IRODR(R) where R is a rotation matrix computes the the
%   inverse Rodrigues' formula of om, returning the rotation matrix R
%   = dehat(Logm(OM)).
%
%   [OM,DOM] = VL_IRODR(R) computes also the derivative of the Rodrigues'
%   formula. In matrix notation this is the expression
%
%          d( dehat logm(vl_hat(R)) )
%     dom = ----------------------.
%                  d(vec R)^T
%
%   [OM,DOM] = VL_IRODR(R) when R is a 9xK matrix repeats the operation
%   for each column (or equivalently matrix with 9*K elements). In
%   this case OM and DOM are arrays with K slices, one per rotation.
%
%   See also: VL_RODR(), VL_HELP().

% Copyright (C) 2007-12 <PERSON> and <PERSON>.
% All rights reserved.
%
% This file is part of the VLFeat library and is made available under
% the terms of the BSD license (see the COPYING file).
