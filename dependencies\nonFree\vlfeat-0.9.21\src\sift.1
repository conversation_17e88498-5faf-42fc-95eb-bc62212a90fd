.TH SIFT 1 "" "VLFeat" "VLFeat"
.\" ------------------------------------------------------------------
.SH NAME
.\" ------------------------------------------------------------------
sift \- Scale Invariant Feature Transform
.\" ------------------------------------------------------------------
.SH SYNOPSIS
.\" ------------------------------------------------------------------
.B sift
.RI [ options ]
FILE .\|.\|.
.\" ------------------------------------------------------------------
.SH OPTIONS
.\" ------------------------------------------------------------------
.TP
.B \-v\fR,\fP \-\^\-verbose
Increase verbosity level (may be repeated).
.TP
.B \-h\fR,\fP \-\^\-help
Show options and version.
.TP
.BI \-\^\-output "\fR[=\fPFILESPEC\fR],\fP " \-o FILESPEC
Enable/specify seeds output.
.TP
.BI \-\^\-frames \fR[=\fPFILESPEC\fR]\fP
Enable/specify frames output.
.TP
.BI \-\^\-descriptors \fR[=\fPFILESPEC\fR]\fP
Enable/specify descriptors output.
.TP
.BI \-\^\-gss \fR[=\fPFILESPEC\fR]\fP
Enable/specify the Gaussian Scale Space (GSS) output.
.TP
.BI \-\^\-meta \fR[=\fPFILESPEC\fR]\fP
Enable/specify meta output.
.TP
.BI \-\^\-first-octave \fR=\fPINTEGER
Specifiy the index of the first octave of the GSS.
.TP
.BI \-\^\-edge-thresh \fR=\fPREAL
Specify the edge threshold.
.TP
.BI \-\^\-peak-thresh \fR=\fPREAL
Specify the peak threshold.
.TP
.BI \-\^\-read-frames \fR[=\fPFILESPEC\fR]\fP
Enable/specify reading the frames from a file.
.B \-\^\-orientations
Force the computation of the frame orientations.
.\" ------------------------------------------------------------------
.SH DESCRIPTION
.\" ------------------------------------------------------------------
.B sift
computes the 
.I Scale Invariant Feature Transform (SIFT).
\. In the simplest case,
.B sift
reads an image file (in PGM format), computes the SIFT frames and
their descriptors and write them to a file. Alternatively,
.B --frames
option can be used to suppress the standard output and produce a file
with the feature frames only.  Similarly,
.B --descriptors
suppresses the standard output and produce a file of descriptors only.
.B --output
can be used in combination with the previous too if the combined
output of frames and descriptors is required.
.P 
The
.B --read-frames
option bypasses the SIFT detector and reads the frames from the
specified file. This option can be used iin combination with
.B --orientations
to read the frames but calculate their orientations anyway. Both option
do not change how the frames and the descriptors are written to disk.
Notice that if
.B --orientations
is used, the number of frames may change if multiple orientations are detected).
.P
A frame is an oriented ellipse (also called affine frame) and is
specified by 6 floating point numbers (in order: coordinate of the
center
.I x
and
.I y,
and elements
.IR A11 ,
.IR A21 ,
.IR A21 ,
.I  A22
of the matrix mapping the unit circle to the ellipse).  A feature
descriptor is an histogram. It is specifyied by 128 non-negative
integer numbers.
.P
.B sift
can process multiple images and derive the names of the various output
files by means of
.I FILESPEC
(see
.BR vlfeat (7)).
Both frames and descriptors can be saved/loaded either in ascii or binary
format.
.
.TP
Ascii format
.
Each descriptor is a list of 128 decimal numbers and
occupies a line of text.  Each frame is a list of 5 numbers,
specifying an elliptical frame, one per line of text.  The combined
output file (with both frames and descriptors) stores a frame
.I and
the corresponding descriptor for each line of text.
.
.TP
Binary format
.
The binary format is similar to the ASCII format, except
that each frame element is stored as an IEEE double floating point
number (eight bytes) and each descriptor element is stored as an
unsiged integer (one byte). The data is written in little
endian order.
.
.\" ------------------------------------------------------------------
.SH EXAMPLES
.\" ------------------------------------------------------------------
.TP
sift test.pgm --peak-thresh=0.03
Caluclates the SIFT frames and descriptors of image
.I /tmp/myimage.pgm
with peak threshold equal to 0.03
and save them back to
.I test.sift 
in ascii format.
.TP 
sift test.pgm --frames
Writes the frames to 
.I 
test.frame
in ascii format and does not compute the descriptors.
.
.TP
sift test.pgm --descriptors=bin://
writes only the frames to
.IR test.descr.
in binary format.
.
.TP
sift test.pgm \-\^\-frames=my.frames -o bin:///tmp/%.sift
writes the frames in ascii format to
.I my.frames 
and the combined frames-descriptors file in binary format to
.IR /tmp/test.sift .
.
.TP
sift test1.pgm test2.pgm \-\^\-read-frames=bin:/tmp/%.key 
reads the
frames of images
.I test1.pgm
and 
.I test2.pgm
from the binary files
.I /tmp/test1.key
and
.I /tmp/test2.key
and writes the frames and their descriptors to the files
.I test1.sift
and
.I test2.sift
respectively.
.
.\" ------------------------------------------------------------------
.SH SEE ALSO
.\" ------------------------------------------------------------------
.BR vlfeat (7).

D. G. Lowe. 
Distinctive image features from scale-invariant keypoints. 
.IR IJCV , 
2(60):91-110, 2004.
