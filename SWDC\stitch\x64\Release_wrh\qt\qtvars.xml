<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
<Qt_DEFINES_>_WINDOWS;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;NDEBUG;QT_NO_DEBUG;QT_CORE_LIB</Qt_DEFINES_>
<Qt_INCLUDEPATH_>D:\Qt\5.15.2\msvc2019_64\include;D:\Qt\5.15.2\msvc2019_64\include\QtCore;D:\Qt\5.15.2\msvc2019_64\mkspecs\win32-msvc</Qt_INCLUDEPATH_>
<Qt_STDCPP_></Qt_STDCPP_>
<Qt_RUNTIME_>MultiThreadedDLL</Qt_RUNTIME_>
<Qt_CL_OPTIONS_>-Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -Zc:__cplusplus</Qt_CL_OPTIONS_>
<Qt_LIBS_>D:\Qt\5.15.2\msvc2019_64\lib\Qt5Core.lib;D:\Qt\5.15.2\msvc2019_64\lib\qtmain.lib;shell32.lib</Qt_LIBS_>
<Qt_LINK_OPTIONS_>"/MANIFESTDEPENDENCY:type='win32' name='Microsoft.Windows.Common-Controls' version='6.0.0.0' publicKeyToken='6595b64144ccf1df' language='*' processorArchitecture='*'"</Qt_LINK_OPTIONS_>
<QMake_QT_SYSROOT_></QMake_QT_SYSROOT_>
<QMake_QT_INSTALL_PREFIX_>D:/Qt/5.15.2/msvc2019_64</QMake_QT_INSTALL_PREFIX_>
<QMake_QT_INSTALL_ARCHDATA_>D:/Qt/5.15.2/msvc2019_64</QMake_QT_INSTALL_ARCHDATA_>
<QMake_QT_INSTALL_DATA_>D:/Qt/5.15.2/msvc2019_64</QMake_QT_INSTALL_DATA_>
<QMake_QT_INSTALL_DOCS_>D:/Qt/Docs/Qt-5.15.2</QMake_QT_INSTALL_DOCS_>
<QMake_QT_INSTALL_HEADERS_>D:/Qt/5.15.2/msvc2019_64/include</QMake_QT_INSTALL_HEADERS_>
<QMake_QT_INSTALL_LIBS_>D:/Qt/5.15.2/msvc2019_64/lib</QMake_QT_INSTALL_LIBS_>
<QMake_QT_INSTALL_LIBEXECS_>D:/Qt/5.15.2/msvc2019_64/bin</QMake_QT_INSTALL_LIBEXECS_>
<QMake_QT_INSTALL_BINS_>D:/Qt/5.15.2/msvc2019_64/bin</QMake_QT_INSTALL_BINS_>
<QMake_QT_INSTALL_TESTS_>D:/Qt/5.15.2/msvc2019_64/tests</QMake_QT_INSTALL_TESTS_>
<QMake_QT_INSTALL_PLUGINS_>D:/Qt/5.15.2/msvc2019_64/plugins</QMake_QT_INSTALL_PLUGINS_>
<QMake_QT_INSTALL_IMPORTS_>D:/Qt/5.15.2/msvc2019_64/imports</QMake_QT_INSTALL_IMPORTS_>
<QMake_QT_INSTALL_QML_>D:/Qt/5.15.2/msvc2019_64/qml</QMake_QT_INSTALL_QML_>
<QMake_QT_INSTALL_TRANSLATIONS_>D:/Qt/5.15.2/msvc2019_64/translations</QMake_QT_INSTALL_TRANSLATIONS_>
<QMake_QT_INSTALL_CONFIGURATION_></QMake_QT_INSTALL_CONFIGURATION_>
<QMake_QT_INSTALL_EXAMPLES_>D:/Qt/Examples/Qt-5.15.2</QMake_QT_INSTALL_EXAMPLES_>
<QMake_QT_INSTALL_DEMOS_>D:/Qt/Examples/Qt-5.15.2</QMake_QT_INSTALL_DEMOS_>
<QMake_QT_HOST_PREFIX_>D:/Qt/5.15.2/msvc2019_64</QMake_QT_HOST_PREFIX_>
<QMake_QT_HOST_DATA_>D:/Qt/5.15.2/msvc2019_64</QMake_QT_HOST_DATA_>
<QMake_QT_HOST_BINS_>D:/Qt/5.15.2/msvc2019_64/bin</QMake_QT_HOST_BINS_>
<QMake_QT_HOST_LIBS_>D:/Qt/5.15.2/msvc2019_64/lib</QMake_QT_HOST_LIBS_>
<QMake_QMAKE_SPEC_>win32-msvc</QMake_QMAKE_SPEC_>
<QMake_QMAKE_XSPEC_>win32-msvc</QMake_QMAKE_XSPEC_>
<QMake_QMAKE_VERSION_>3.1</QMake_QMAKE_VERSION_>
<QMake_QT_VERSION_>5.15.2</QMake_QT_VERSION_>
<QtBkup_QtHash>jZFBjsIwDEWvkhNQMRJLFsb+UwxpknHcBRoh7n8L0qqo0mwmC2/i/7/9nF8VJFd/nH/8dTocT4evwNkQLprWEnxrQj0PxfIN7IM6pvVRXXMaLc9l4Mh5KhrRVGhCRq3ZdlUNmjjOgkJ+7Y0ikXUExc0sam2BbIoaqguX0hsVKY0zjahOScgk2NygJ/T6N3nUi5E9AsdXLgt991l2ls0YWtY/7oZ9H3ZjOx5S+yxe8Jde3w5/Uj7jDRFU8XwD</QtBkup_QtHash>
    <QtVersion>5.15.2</QtVersion>
    <QtVersionMajor>5</QtVersionMajor>
    <QtVersionMinor>15</QtVersionMinor>
    <QtVersionPatch>2</QtVersionPatch>
  </PropertyGroup>
</Project>
