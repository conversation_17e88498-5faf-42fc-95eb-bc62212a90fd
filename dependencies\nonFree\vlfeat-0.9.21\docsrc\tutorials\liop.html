<!DOCTYPE group PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<group>

%tableofcontents;

<p>This tutorial shows how to extract <a href='%dox:liop;'>Local
Intensity Order Pattern</a> (LIOP) deascriptors using VLFeat. LIOP is
implemented in the <code>vl_liop</code> as well as
in <code>vl_covdet</code>.</p>

<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
<h1 id="tut.liop.computation">LIOP descriptor computation</h1>
<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->

<p>A LIOP descriptor can be computed from a square, gray scale image
with and odd side length <code>patch</code>. This is usually not a
restriction since local patches are obtained by cropping and warpinig
an input image. The descriptor is computed using
the <code>vl_liop</code> function:</p>

<precode type='matlab'>
descr = vl_covdet(patch) ;
</precode>

<p>You can use the verbose option <code>verbose</code> if you wish to
see the parametr and descriptor details.</p>

<precode type='matlab'>
descr = vl_covdet(patch,'Verbose') ;
</precode>

<p><code>patch</code> can be a 3D array, with one image per 2D
layer. In this case, <code>descr</code> is a matrix with as many
columns as images. LIOP is also integrated
into <code>vl_covdet</code>.</p>

<p><code>vl_liop</code> allows full customization of the LIOP
descriptor parameters. These are
discussed <a href="%dox:liop-fundamentals;">here</a>. A parameter that
is commonly tweaked is the <em>intensity threshold</em> to downweight
unstalbe intensity order patterns in the descriptor computation. For
example</p>

<precode type='matlab'>
descr = vl_covdet(patch,'IntensityThreshold', 0.1) ;
</precode>

<p>set this threshold to 0.1.</p>
</group>
