<!DOCTYPE group PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<group>

<p>The latest version of VLFeat is <em>%env:VERSION;</em>. To use
VLFeat, simply download and unpack the latest binary package and add
the appropriate paths to your environment (see below for details).</p>

<table class="boxes twocols">
<tr>
<td>
<h1>Downloads</h1>
<ul>
 <li><b><a href="%pathto:root;download/vlfeat-%env:VERSION;-bin.tar.gz"
           onClick="javascript:
           pageTracker._trackPageview('/download/vlfeat-%env:VERSION;-bin.tar.gz');">VLFeat
           %env:VERSION; binary package</a></b></li>
 <li><a href="%pathto:root;download/vlfeat-%env:VERSION;.tar.gz"
        onClick="javascript:
        pageTracker._trackPageview('/download/vlfeat-%env:VERSION;.tar.gz');">VLFeat
        %env:VERSION; source code only</a></li>
 <li><a href="%pathto:root;download/">Previous versions ...</a></li>
</ul>
</td>
<td>
<h1>Install</h1>
<ul>
<li><a href="%pathto:install.matlab;">MATLAB toolbox</a></li>
<li><a href="%pathto:install.shell;">Command line tools</a></li>
<li><a href="%pathto:install.c;">C library</a></li>
</ul>
</td>
</tr>
</table>

<div class="clear">&nsbp;</div>

<h1>Repository access</h1>

<p>VLFeat is under active development. You
can <a href="http://github.com/vlfeat/vlfeat/tree/master">browse our
Git repository</a> or download it by</p>

<precode type='matlab'>git clone git://github.com/vlfeat/vlfeat.git</precode>

<p>(This will require <a href="http://www.git-scm.org/">Git</a> to be
installed). The top of the master branch corresponds to the most
recent version of VLFeat, but it could be unstable.</p>

<p>We welcome contributions to both the documentation and the source
code of VLFeat. You can create patches against our Git repository and
send them to us for inclusion in the next version. There are two ways
to contribute. For minor changes, simply clone our public repository,
as explained above. Once you have made and committed your changes
locally, you can submit them to the project via e-mail using a command
like <code>git format-patch</code>:
</p>

<precode type='matlab'>git format-patch origin</precode>

<p>For major additions, we prefer to handle collaboration through
<a href="http://github.com/">github</a>. Follow
<a href="http://github.com/guides/fork-a-project-and-submit-your-modifications">their tutorial</a>
to fork <a href="http://github.com/vlfeat/vlfeat/tree/master">our project</a>
and submit your modifications using a pull request.</p>

</group>
