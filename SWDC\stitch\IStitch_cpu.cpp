#include "IStitch_cpu.hpp"

namespace SWDC {
	namespace stitch {

		void IStitch_cpu::genCorrectionImage(const image::Image<Vec3c>& image, const std::shared_ptr<camera::IntrinsicBase> camera, const data::Pose3& pose, image::Image<Vec3c>* imageout)
		{
			Quadrangle quadrangle = getGroundQuadrangle(camera, pose);
			double xmin = std::min({ quadrangle.leftTop(0), quadrangle.rightTop(0), quadrangle.rightBottom(0), quadrangle.leftBottom(0) });
			double xmax = std::max({ quadrangle.leftTop(0), quadrangle.rightTop(0), quadrangle.rightBottom(0), quadrangle.leftBottom(0) });

			double ymin = std::min({ quadrangle.leftTop(1), quadrangle.rightTop(1), quadrangle.rightBottom(1), quadrangle.leftBottom(1) });
			double ymax = std::max({ quadrangle.leftTop(1), quadrangle.rightTop(1), quadrangle.rightBottom(1), quadrangle.leftBottom(1) });

			int width = fabs(xmax - xmin) / _groundSampleSize;
			int height = fabs(ymax - ymin) / _groundSampleSize;

			(*imageout).resize(width, height, image.Channels());

			Mat5X tmp;
			tmp.resize(5, static_cast<int64_t>(width) * static_cast<int64_t>(height));

#pragma omp parallel for
			for (int i = 0; i < height; i++)
			{
				double y = ymax - (i + 0.5) * _groundSampleSize;
				for (int j = 0; j < width; j++)
				{
					int col = j + i * width;
					tmp(0, col) = xmin + (j + 0.5) * _groundSampleSize;
					tmp(1, col) = y;
					tmp(2, col) = _averageHeight;
					tmp(3, col) = i;
					tmp(4, col) = j;
				}
			}

			int numCols = 10000;
			int numSubMat = tmp.cols() / numCols;

			int totalCols = numCols * numSubMat;
			int blockNum = totalCols < tmp.cols() ? numSubMat + 1 : numSubMat;

			std::vector<Mat3X> mat3XList(blockNum);
			std::vector<Mat2X> mat2XList(blockNum);

			//#pragma omp parallel for num_threads(blockNum)
			for (int i = 0; i < numSubMat; ++i) {
				int startColIndex = i * numCols;
				mat3XList[i] = tmp.block(0, startColIndex, 3, numCols);
				mat2XList[i] = tmp.block(3, startColIndex, 2, numCols);
			}

			if (totalCols < tmp.cols())
			{
				mat3XList.push_back(tmp.block(0, totalCols, 3, tmp.cols() - totalCols));
				mat2XList.push_back(tmp.block(3, totalCols, 2, tmp.cols() - totalCols));
			}

			std::vector<std::shared_ptr<Mat2X>> mat2XPtrList;
			mat2XPtrList.resize(mat3XList.size());

#pragma omp parallel for schedule(dynamic)
			for (int i = 0; i < mat3XList.size(); i++)
			{
				mat2XPtrList.at(i) = camera->cam2ima(*camera->grd2cam(mat3XList.at(i), pose));
			}

#pragma omp parallel for
			for (int i = 0; i < mat2XPtrList.size(); i++)
			{
				Mat2X imgBlockList = *mat2XPtrList.at(i);
				Mat2X posBlockList = mat2XList.at(i);
				for (int j = 0; j < imgBlockList.cols(); j++)
				{
					if (imgBlockList(0, j) < image.Height() && imgBlockList(0, j) >= 0 && imgBlockList(1, j) < image.Width() && imgBlockList(1, j) >= 0)
					{
						switch (_tnterpolation)
						{
						case SWDC::stitch::ETnterpolation::NEAREST:
							imgBlockList(0, j) = std::round(imgBlockList(0, j));
							imgBlockList(1, j) = std::round(imgBlockList(1, j));
							if (imgBlockList(0, j) < image.Height() && imgBlockList(0, j) >= 0 && imgBlockList(1, j) < image.Width() && imgBlockList(1, j) >= 0)
							{
								(*imageout)(posBlockList(0, j), posBlockList(1, j)) = image(imgBlockList(0, j), imgBlockList(1, j));
							}
							else
							{
								(*imageout)(posBlockList(0, j), posBlockList(1, j)) = { 0, 0, 0 };
							}
							break;
						case SWDC::stitch::ETnterpolation::BILINEAR:
							(*imageout)(posBlockList(0, j), posBlockList(1, j)) = bilinearInterpolation(imgBlockList(0, j), imgBlockList(1, j), image);
							break;
						}
					}
					else
					{
						(*imageout)(posBlockList(0, j), posBlockList(1, j)) = { 0, 0, 0 };
					}
				}
			}
		}
		
		void IStitch_cpu::genOverlapRegionFeature(const image::Image<Vec3c>& image, const std::shared_ptr<camera::IntrinsicBase> camera, const data::Pose3& pose, const Quadrangle& overlapRectangle, const std::shared_ptr<feature::ImageDescriber> imageDescriber, std::unique_ptr<feature::Regions>& regions)
		{
			imageDescriber->allocate(regions);
			int overlapWidth = ceil(fabs(overlapRectangle.leftTop(0) - overlapRectangle.rightTop(0)) / _groundSampleSize);
			int overlapHeight = ceil(fabs(overlapRectangle.leftTop(1) - overlapRectangle.leftBottom(1)) / _groundSampleSize);

			int widthBlock = overlapWidth;
			int heightBlock = overlapHeight;

			bool widthCut = true;

			int cutNum = 10;

			if (overlapWidth > overlapHeight) {
				widthBlock = floor(overlapWidth / cutNum);
				widthCut = true;
			}
			else
			{
				heightBlock = floor(overlapHeight / cutNum);
				widthCut = false;
			}

			std::vector<image::Image<float>> overlapImages;
			overlapImages.resize(cutNum);

#pragma omp parallel for schedule(dynamic)
			for (int k = 0; k < cutNum; k++)
			{
				image::Image<float> overlapImage;
				Mat3X tmp;

				overlapImage.resize(widthBlock, heightBlock, 1);
				tmp.resize(3, static_cast<int64_t>(widthBlock) * static_cast<int64_t>(heightBlock));

				if (widthCut) {
					for (int i = 0, col = 0; i < heightBlock; i++)
					{
						for (int j = widthBlock * k; j < widthBlock * (k + 1); j++, col++)
						{
							tmp(0, col) = overlapRectangle.leftTop(0) + (j + 0.5) * _groundSampleSize;
							tmp(1, col) = overlapRectangle.leftTop(1) - (i + 0.5) * _groundSampleSize;
							tmp(2, col) = _averageHeight;
						}
					}
				}
				else
				{
					for (int i = heightBlock * k, col = 0; i < heightBlock * (k + 1); i++)
					{
						for (int j = 0; j < widthBlock; j++, col++)
						{
							tmp(0, col) = overlapRectangle.leftTop(0) + (j + 0.5) * _groundSampleSize;
							tmp(1, col) = overlapRectangle.leftTop(1) - (i + 0.5) * _groundSampleSize;
							tmp(2, col) = _averageHeight;
						}
					}
				}

				Mat2X img = *camera->cam2ima(*camera->grd2cam(tmp, pose));

				for (int i = 0, col = 0; i < heightBlock; i++)
				{
					for (int j = 0; j < widthBlock; j++, col++)
					{
						if (img(0, col) < image.Height() && img(0, col) >= 0 && img(1, col) < image.Width() && img(1, col) >= 0)
						{
							switch (_tnterpolation)
							{
							case SWDC::stitch::ETnterpolation::NEAREST:
								img(0, col) = std::round(img(0, col));
								img(1, col) = std::round(img(1, col));
								if (img(0, col) < image.Height() && img(0, col) >= 0 && img(1, col) < image.Width() && img(1, col) >= 0)
								{
									overlapImage(i, j) = rgbToGrayscale(image(img(0, col), img(1, col))(0), image(img(0, col), img(1, col))(1), image(img(0, col), img(1, col))(2)) / 255.f;
								}
								else
								{
									overlapImage(i, j) = 0;
								}
								break;
							case SWDC::stitch::ETnterpolation::BILINEAR:
								Vec3c pixel = bilinearInterpolation(img(0, col), img(1, col), image);
								overlapImage(i, j) = rgbToGrayscale(pixel(0), pixel(1), pixel(2)) / 255.f;
								break;
							}
						}
						else
						{
							overlapImage(i, j) = 0;
						}
					}
				}

				overlapImages.at(k) = overlapImage;
			}

			for (int i = 0; i < overlapImages.size(); i++)
			{
				std::unique_ptr<feature::Regions> overlapImageRegions;
				imageDescriber->allocate(overlapImageRegions);
				imageDescriber->describe(overlapImages.at(i), overlapImageRegions);
				feature::PointFeatures& pointFeatures = overlapImageRegions->Features();

				for (int j = 0; j < pointFeatures.size(); j++)
				{
					if (widthCut) {
						pointFeatures.at(j).x() += i * widthBlock;
					}
					else
					{
						pointFeatures.at(j).y() += i * heightBlock;
					}
				}
				regions->AddRegion(overlapImageRegions.get());
			}
		}
	}
}
