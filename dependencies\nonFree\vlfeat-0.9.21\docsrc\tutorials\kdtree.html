<!DOCTYPE group PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<group>

%tableofcontents;

<p><b>VLFeat</b> implements the randomized kd-tree forest from
<a href="http://www.cs.ubc.ca/~mariusm/index.php/FLANN/FLANN">FLANN</a>.
This enables fast medium and large scale nearest neighbor queries
among high dimensional data points (such as those produced by SIFT).
</p>

<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
<h1 id="tut.kdtree.introduction">Introduction</h1>
<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->

<p>A kd-tree is a data structure used to quickly solve
nearest-neighbor queries. Consider a set of 2D points uniformly
distributed in the unit square:</p>

<precode type='matlab'>
  X = rand(2, 100) ;
</precode>

<p>A kd-tree is generated by using the <code>vl_kdtreebuild</code> function:
</p>

<precode type='matlab'>
  kdtree = vl_kdtreebuild(X) ;
</precode>

<p>The returned <code>kdtree</code> indexes the set of
points <code>X</code>. Given a query point <code>Q</code>, the
function <code>vl_kdtreequery</code> returns its nearest neighbor
in <code>X</code>:</p>

<precode type='matlab'>
  Q = rand(2, 1) ;
  [index, distance] = vl_kdtreequery(kdforest, X, Q) ;
</precode>

<p>Here <code>index</code> stores the index of the column
of <code>X</code> that is closest to the point <code>Q</code>.
<code>distance</code> is the squared euclidean distance between <code>X(index),Q</code>.</p>

<p>A kd-tree is a hierarchal structure built by partitioning the data
recursively along the dimension of maximum variance. At
each iteration the variance of each column is computed and the data is
split into two parts on the column with maximum variance. The
splitting threshold can be selected to be the mean or the median (use
the <code>ThresholdMethod</code> option of
<code>vl_kdtreebuild</code>).</p>

<div class="figure">
 <img src="%pathto:root;demo/kdtree_uniform_mean.jpg"/>
 <img src="%pathto:root;demo/kdtree_uniform_median.jpg"/>
 <div class="caption">
  <span class="content">
   kd-tree partitions of a uniform set of data points, using
   the mean (left image) and the median (right image) thresholding
   options of <code>vl_kdtreebuild</code>. On the bottom right corner
   a query point is marked along with the ten closest neighbors as
   found by <code>vl_kdtreequery</code>. Figure generated
   by <code>vl_demo_kdtree</code>.
  </span>
 </div>
</div>

<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
<h1 id="tut.kdtree.querying">Querying</h1>
<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->

<p><code>vl_kdtreequery</code> uses a best-bin first search
heuristic. This is a branch-and-bound technique that maintains an
estimate of the smallest distance from the query point to any of the
data points down all of the open paths.</p>

<p><code>vl_kdtreequery</code> supports two important operations:
  <em>approximate nearest-neighbor search</em> and <em>k-nearest
  neighbor search</em>. The latter can be used to return the
  <em>k</em> nearest neighbors to a given query point <code>Q</code>.
  For instance:
</p>

<precode type='matlab'>
[index, distance] = vl_kdtreequery(kdtree, X, Q, 'NumNeighbors', 10) ;
</precode>

<p>returns the closest 10 neighbors to <code>Q</code>
in <code>X</code> and their distances, stored along the columns of
<code>index</code> and <code>distance</code>.</p>

<p>The <code>MaxComparisons</code> option is used to run an ANN query.
The parameter specifies how many paths in the best-bin-first search of
the kd-tree can be checked before giving up and returning the closest
point encountered so far. For instance:</p>

<precode type='matlab'>
[index, distance] = vl_kdtreequery(kdtree, X, Q, 'NumNeighbors', 10, 'MaxComparisons', 15) ;
</precode>

<p>does not compare any point in <code>Q</code> with more than 15
points in <code>X</code>.</p>

<div class="figure">
 <img src="%pathto:root;demo/kdtree_ann_1.jpg"/>
 <img src="%pathto:root;demo/kdtree_ann_2.jpg"/>
 <img src="%pathto:root;demo/kdtree_ann_3.jpg"/>
 <img src="%pathto:root;demo/kdtree_ann_4.jpg"/>
 <div class="caption">
  <span class="content">
    Finding the 10 approximated nearest neighbors for increasing
    values of the <code>MaxComparisons</code> parameter. Note that at
    most <code>MaxComparisons</code> neighbors can be returned (if more
    are requested, they are ignored). Figure generated
    by <code>vl_demo_kdtree_ann</code>.
  </span>
 </div>
</div>

<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
<h1 id="tut.kdtree.forest">Randomized kd-tree forests</h1>
<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->

<p>VLFeat supports constructing randomized <em>forests</em> of
kd-trees to improve the effectiveness of the representation in high
dimensions. The parameter <code>NumTrees</code> of
<code>vl_kdtreebuild</code> specifies how many trees to use in
constructing the forest. Each tree is constructed independently.
Instead of always splitting on the maximally variant dimension, each
tree chooses randomly among the top five most variant dimensions at
each level. When querying, <code>vl_kdtreequery</code> runs
best-bin-first across all the trees in parallel. For instance</p>

<precode type='matlab'>
  kdtree = vl_kdtreebuild(X, 'NumTrees', 4) ;
  [index, distance] = vl_kdtreequery(kdtree, X, Q) ;
</precode>

<p>constructs four trees and queries them.</p>

<div class="figure">
 <img src="%pathto:root;demo/kdtree_forest_tree_1.jpg"/>
 <img src="%pathto:root;demo/kdtree_forest_tree_2.jpg"/>
 <img src="%pathto:root;demo/kdtree_forest_tree_3.jpg"/>
 <img src="%pathto:root;demo/kdtree_forest_tree_4.jpg"/>
 <div class="caption">
  <span class="content">
    The parameter <code>NumTrees</code>
    tells <code>vl_kdtreebuild</code> to construct a number of
    randomized kd-trees. Figure generated
    by <code>vl_demo_kdtree_forest</code>.
  </span>
 </div>
</div>


</group>
