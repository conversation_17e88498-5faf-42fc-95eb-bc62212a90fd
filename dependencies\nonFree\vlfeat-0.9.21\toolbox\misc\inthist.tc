/** @file inthist.tc
 ** @brief vl_intist.c helper
 ** <AUTHOR>
 **/

/*
Copyright (C) 2007-12 <PERSON> and <PERSON>.
All rights reserved.

This file is part of the VLFeat library and is made available under
the terms of the BSD license (see the COPYING file).
*/

#define JOIN_(a,b) a ## b
#define JOIN(a,b) JOIN_(a,b)

#undef INTEGRAL
#define INTEGRAL JOIN(integral_, SFX)

/*
   This function is 'transpose safe': The integral histogram SRC can
   be either column major or row major, as long as WIDTH is the
   fastest varying dimension.
*/
void INTEGRAL (T* dst,
               int dstStride,
               T const* src,
               int srcWidth, int srcHeight, int srcStride)
{
  int x, y ;
  *dst = *src ;
  dst += 1 ;
  src += 1 ;

  for (x = 1 ; x < srcWidth ; ++ x) {
    *dst = *(dst - 1)  + *src ;
    dst += 1 ;
    src += 1 ;
  }

  for (y = 1 ; y < srcHeight ; ++ y) {
    dst += dstStride - srcWidth ;
    src += srcStride - srcWidth ;

    *dst = *(dst - dstStride) + *src ;
    dst += 1 ;
    src += 1 ;

    for (x = 1 ; x < srcWidth ; ++ x) {
      *dst =  *(dst - dstStride) - *(dst - dstStride - 1) +  *(dst - 1)  + *src ;
      dst += 1 ;
      src += 1 ;
    }
  }
}
