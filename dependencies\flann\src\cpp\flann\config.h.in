/***********************************************************************
 * Software License Agreement (BSD License)
 *
 * Copyright 2008-2011  <PERSON> (ma<PERSON><PERSON>@cs.ubc.ca). All rights reserved.
 * Copyright 2008-2011  <PERSON> (<EMAIL>). All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * 1. Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE AUTHOR ``AS IS'' AND ANY EXPRESS OR
 * IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 * IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
 * NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 * DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 * THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF
 * THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *************************************************************************/


#ifndef FLANN_CONFIG_H_
#define FLANN_CONFIG_H_

#ifdef FLANN_VERSION_
#undef FLANN_VERSION_
#endif
#define FLANN_VERSION_ "${FLANN_VERSION}"

#ifdef FLANN_VERSION_MAJOR_
#undef FLANN_VERSION_MAJOR_
#endif
#define FLANN_VERSION_MAJOR_ ${FLANN_VERSION_MAJOR}

#ifdef FLANN_VERSION_MINOR_
#undef FLANN_VERSION_MINOR_
#endif
#define FLANN_VERSION_MINOR_ ${FLANN_VERSION_MINOR}

#ifdef FLANN_VERSION_PATCH_
#undef FLANN_VERSION_PATCH_
#endif
#define FLANN_VERSION_PATCH_ ${FLANN_VERSION_PATCH}


#endif /* FLANN_CONFIG_H_ */
