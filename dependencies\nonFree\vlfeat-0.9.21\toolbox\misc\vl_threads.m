%VL_THREADS  Control VLFeat computational threads
%   [NUM,MAXNUM] = VL_THREADS() returns the current number of
%   computational threads NUM and the maximum possible number MAXNUM.
%
%   VL_THREADS(NUM) sets the current number of threads to the
%   specified value. NUM = VL_THREADS(NUM) does the same, but returns
%   the *previous* number of computational threads as well.
%
%   See also: VL_HELP().

% Copyright (C) 2013 Andrea <PERSON>.
% All rights reserved.
%
% This file is part of the VLFeat library and is made available under
% the terms of the BSD license (see the COPYING file).
