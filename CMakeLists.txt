cmake_minimum_required(VERSION 3.15)
project(SWDC_MSBuild NONE)

# 通过 CMake 的 add_custom_target 调用 bat 脚本和 msbuild
add_custom_target(
    build_swdc ALL
    COMMAND call "D:/Program Files (x86)/Microsoft Visual Studio/2019/Professional/Common7/Tools/VsDevCmd.bat" &&
            msbuild SWDC.sln /p:Configuration=Release_wrh /p:Platform=x64 -m
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
    COMMENT "调用 VS2019 环境并用 msbuild 编译 SWDC.sln (Release_wrh|x64)"
)
