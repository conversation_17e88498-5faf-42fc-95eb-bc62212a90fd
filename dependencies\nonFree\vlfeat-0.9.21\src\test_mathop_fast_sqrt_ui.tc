#define FL VL_XCAT(VL_FL_INT, SFX)

{
  T x, acc, acc_ ;
  double elaps ;
  int neval  ;

  vl_tic() ;
  neval = 0 ;
  x = 0 ;
  acc = 0 ;
  do {
    T y  = VL_XCAT(vl_fast_sqrt_ui, SFX) (x) ;
    acc += y ;
    x += 1 << STEP ;
    neval += 1 ;
  } while (x > (1 << STEP) - 1) ;
  elaps = vl_toc() ;
  VL_PRINTF ("%20s", "vl_fast_sqrt_ui" VL_XSTRINGIFY(SFX)) ;
  VL_PRINTF (" %10.2f %10.2g %010" FL "x\n", elaps, (double) neval / elaps, acc) ;

  vl_tic() ;
  neval = 0 ;
  x = 0 ;
  acc_ = 0 ;
  do {
    vl_uint32 y  = floor(sqrt((double) x));
    acc_ += y ;
    x += 1 << STEP ;
    neval += 1 ;
  } while (x > (1 << STEP) - 1) ;
  elaps = vl_toc() ;
  VL_PRINTF ("%20s", "baseline") ;
  VL_PRINTF (" %10.2f %10.2g %010" FL "x\n", elaps, (double) neval / elaps, acc) ;


  if (acc != acc_) {
    VL_PRINTF ("error: vl_fast_sqrt_ui" VL_XSTRINGIFY(SFX) " != floor(sqrt((double) x))\n") ;
    error = 1 ;
  }
}

#undef FL
