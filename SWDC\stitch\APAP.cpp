#include "APAP.hpp"
#include <numeric.hpp>

namespace SWDC {
	namespace stitch {


		MatrixXd calculate_Wi_forPoint(double x, double y)
		{
			const double sigma_squared = sigma * sigma;
			MatrixXd Wi(2 * points.size(), 2 * points.size());
			Wi.setZero();
			for (size_t i = 0; i < points.size(); i++)
			{
				double u = (double)points[i].x, v = (double)points[i].y;
				double sqr_dist = getSqrDist(x, y, u, v);
				double candidate = exp(-sqr_dist / sigma_squared);
				double omega_i = max(candidate, gamma);
				Wi(i * 2, i * 2) = omega_i;
				Wi(i * 2 + 1, i * 2 + 1) = omega_i;
			}
			return Wi;
		}

		void APAP::calculateWeightMatrices(const image::Image<Vec3c>& image)
		{
			//points = obj;
			int Width = image.Width(), Height = image.Height();
			Eigen::ArrayXd heightArray = Eigen::ArrayXd::LinSpaced(_gridSize + 1, 0, Height - 1);
			Eigen::ArrayXd widthArray = Eigen::ArrayXd::LinSpaced(_gridSize + 1, 0, Width - 1);

			int count = 0;
			for (int i = 0; i < _gridSize; i++) {
				double y = (heightArray(i) + heightArray(i + 1)) / 2;
				for (int j = 0; j < _gridSize; j++) {
					double x = (widthArray(j) + widthArray(j + 1)) / 2;
					MatrixXd Wi = calculate_Wi_forPoint(x, y);
					vec.push_back(Wi);
				}
			}
		}
	}
}