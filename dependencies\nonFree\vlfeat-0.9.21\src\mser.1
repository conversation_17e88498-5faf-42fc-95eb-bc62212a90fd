.TH MSER 1 "" "VLFeat" "VLFeat"
.\" ------------------------------------------------------------------
.SH NAME
.\" ------------------------------------------------------------------
mser \- Maximally Stable Extremal Regions
.\" ------------------------------------------------------------------
.SH SYNOPSIS
.\" ------------------------------------------------------------------
.B mser
.RI [ options ]
FILE .\|.\|.
.\" ------------------------------------------------------------------
.SH OPTIONS
.\" ------------------------------------------------------------------
.TP
.B \-v\fR,\fP \-\^\-verbose
Increase verbosity level (may be repeated).
.TP
.B \-h\fR,\fP \-\^\-help
Show options and version.
.TP
.BI \-\^\-seeds \fR[=\fPFILESPEC\fR]\fP
Enable/specify seeds output.
.TP
.BI \-\^\-frames \fR[=\fPFILESPEC\fR]\fP
Enable/specify frames output.
.TP
.BI \-\^\-meta \fR[=\fPFILESPEC\fR]\fP
Enable/specify meta output.
.TP
.BI \-d " REAL" "\fR,\fP \-\^\-delta" "=REAL"
Specify delta parameter.
.TP
.BI \-\^\-min-area \fR=\fPREAL
Specify minimum region area.
.TP
.BI \-\^\-max-area \fR=\fPREAL
Specify maximum region area.
.TP
.BI \-\^\-max-variation \fR=\fPREAL
Specify maximum region variation.
.TP
.BI \-\^\-min-diversity \fR=\fPREAL
Specify minimum region diversity.
.\" ------------------------------------------------------------------
.SH DESCRIPTION
.\" ------------------------------------------------------------------
.B mser
computes the 
.I Maximally Stable Extremal Regions (MSER)
\. In the simplest case,
.B mser 
reads an image file (in PGM format), computes the MSERs, and writes
them to a file of region seeds. Alternatively, the
.B --frames
option can be used to compute elliptical frames instead of region seeds.
It is also possible to use
.B --frames
and
.B --seeds
in combination to save both seeds and frames.
.
.P
A 
.IR region\ seed 
is and index pointing to one of the image pixels (pixels are
enumerated in row major order, with 0 corresponding to the upper-left
corner of the image). An MSER containing a given seed is the largest
connect subset of pixels whose value is not smaller than the seed
value.
.
.P
A 
.IR frame 
is an ellipse approximating an MSER and is specified by 5 floating
point numbers: coordinate of the center
.I x
and
.I y,
and elements
.IR S11 ,
.IR S12 ,
.IR S22
of the co-variance matrix. 
.P
Both seeds and frames can be saved either in ascii or binary format, based
on the specified 
.IR FILESPEC s
(see also
.BR vlfeat (7)):
.
.TP
Ascii format
.
Each seed is a decimal number, one per line of text, and each
frame is a list of 6 floating point numbers, one per line of text.
.
.TP 
Binary format
.
The binary format is similar to the ascii format, except that each
seed is stored as a unsigned integer (four bytes) and each frame
component is stored as an IEEE double (eight bytes). The data is
written in little endian order.
.
.P
.B mser
can process multiple images. In this case the names of the
corresponding output files are calculated based on
.IR FILESPEC s.
.\" ------------------------------------------------------------------
.SH EXAMPLES
.\" ------------------------------------------------------------------
.
.TP
mser test.pgm -d 5
Computes the MSERs of image
.I test.pgm
setting the parameter
.I delta
to 5
and save the result as a sequence of region seeds to
.IR test.mser .
.TP
mser test.pgm \-\^\-frames
Computes the MSERs of image
.IR test.pgm , 
fits elliptical frames to them and saves the frames to
.IR test.frame .
.
.TP
mser test.pgm \-\^\-frames=bin:///tmp/%.frame \-\^\-seeds
Computes the MSERs of image
.IR test.pgm , 
fits elliptical frames to them and saves
the frames to 
.I /tmp/test.frame
and the region seeds to
.IR test.mser .
.
.\" ------------------------------------------------------------------
.SH SEE ALSO
.\" ------------------------------------------------------------------
.P
.BR vlfeat (7)
.
.P
J. Matas, O. Chum, M. Urban, and T. Pajdla. 
Robust wide baseline stereo from maximally stable extremal regions. In
.IR "Proc. BMVC" , 
2002.
.
