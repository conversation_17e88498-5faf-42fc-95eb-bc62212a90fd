import QtQuick 2.12
import QtQuick.Window 2.12
import QtQuick.Layouts 1.14
import QtQuick.Controls 1.4
import QtQuick.Controls 2.14
import QtQuick.Dialogs 1.3
import Qt.labs.settings 1.0

Window {
    property var platform:null

    id:imgstitch
    flags: Qt.Window
    visible: true
    width: 640
    height: 655
    minimumHeight: 655
    maximumHeight: 655
    minimumWidth: 640
    maximumWidth: 640
    color: "#FFF"
    title: "IMGStitch V1.0.0"
    Rectangle{
        color: "#ffffff"
        anchors.fill: parent

        Rectangle {
            id: rectangle
            color: "#ffffff"
            width: parent.width
            height: 550
            anchors.top: parent.top
            anchors.left: parent.left
            anchors.right: parent.right
            anchors.leftMargin: 5
            anchors.topMargin: 5
            anchors.rightMargin: 5
            border.width: 1
            border.color: "#DCDFE6"

            Rectangle {
                id:prectangle
                color: "#ffffff"
                height: 28
                anchors.top: parent.top
                anchors.left: parent.left
                anchors.right: parent.right
                anchors.topMargin: 5
                anchors.leftMargin: 5
                anchors.rightMargin: 5

              WButton{
                    id:pbutton
                    text: "平台参数"
                    width: 100
                    font.family: "Verdana"
                    anchors.right: parent.right
                    anchors.top: parent.top
                    anchors.bottom: parent.bottom
                    anchors.topMargin: 2
                    anchors.bottomMargin: 2
                    anchors.rightMargin: 2

                    onClickButton: {
                        platformfileDialog.open()
                    }
                }

                Rectangle{
                    anchors.bottom: parent.bottom
                    anchors.top: parent.top
                    anchors.left: parent.left
                    anchors.right: pbutton.left
                    anchors.bottomMargin: 0
                    anchors.topMargin: 0
                    anchors.leftMargin: 2
                    anchors.rightMargin: 5
                    color: "#C5C8CE"
                    Text {
                        id: platformtext
                        opacity: 1
                        text:"请选择平台参数文件"
                        anchors.fill: parent
                        anchors.leftMargin: 5
                        anchors.rightMargin: 5
                        elide:Text.ElideRight
                        verticalAlignment: Text.AlignVCenter
                        horizontalAlignment: Text.AlignLeft
                        font.pixelSize: 14
                        font.family: "Verdana"
                        color: "#757575"
                    }
                }

            }

            Rectangle {
                id: frectangle
                height: 200
                color: "#ffffff"
                border.width: 1
                border.color: "#DCDFE6"
                anchors.top: prectangle.bottom
                anchors.left: parent.left
                anchors.right: parent.right
                anchors.topMargin: 10
                anchors.leftMargin: 5
                anchors.rightMargin: 5

                ComboBox {
                    id: comboBox
                    width: parent.width / 2.0
                    height: 35
                    textRole: "text"
                    valueRole: "value"
                    currentIndex: 0
                    font.pixelSize: 13
                    font.family: "Verdana"
                    anchors.top: parent.top
                    anchors.topMargin: 5
                    anchors.left: parent.left
                    anchors.leftMargin: 5

                    model: ListModel {
                        id:  cameraComboBoxModel
                        ListElement{text:"请选择相机"; value:"-1"}
                    }

                    onCurrentTextChanged: {
                        if(currentIndex - 1 >= 0){
                            var platform = imgstitch.platform[(currentIndex - 1).toString()];
                            rotationText.text = platform["rotationText"];
                            distortionText.text = platform["distortionText"];
                            if(platform["imagePath"]){
                                imagepathText.text = platform["imagePath"]
                            }else{
                                imagepathText.text = "请选择影像数据集";
                            }
                        }else{
                            rotationText.text = "";
                            distortionText.text = "";
                            imagepathText.text = "请选择影像数据集";
                        }
                    }
                }

                Text {
                    id:rotationText
                    text: ""
                    font.family: "Verdana"
                    font.weight: Font.Light
                    font.bold: false
                    anchors.top: parent.top
                    anchors.left: comboBox.right
                    anchors.right: parent.right
                    anchors.bottomMargin: 0
                    anchors.topMargin: 5
                    anchors.leftMargin: 10
                    height: comboBox.height
                    verticalAlignment: Text.AlignVCenter
                    horizontalAlignment: Text.AlignLeft
                    font.pixelSize: 14
                }

                Rectangle {
                    id:imgmrectangle
                    height: 90
                    anchors.top: comboBox.bottom
                    anchors.left: parent.left
                    anchors.right: parent.right
                    anchors.topMargin: 5
                    anchors.leftMargin: 5
                    anchors.rightMargin: 5
                    anchors.bottomMargin: 10
                    border.color: "#DCDFE6"
                    border.width: 1

                    Text {
                        id:distortionText
                        text: ""
                        font.family: "Verdana"
                        anchors.top: parent.top
                        anchors.left: parent.left
                        anchors.leftMargin: 5
                        height: comboBox.height
                        verticalAlignment: Text.AlignVCenter
                        horizontalAlignment: Text.AlignLeft
                        font.weight: Font.Light
                        font.pixelSize: 12
                    }

                    Item {
                        height: 25
                        anchors.bottom: parent.bottom
                        anchors.left: parent.left
                        anchors.right: parent.right
                        anchors.bottomMargin: 5
                        WButton {
                            id: imagebutton
                            text: "影像数据集"
                            width: 80
                            font.pixelSize: 12
                            anchors.top: parent.top
                            anchors.bottom: parent.bottom
                            anchors.left: parent.left
                            anchors.leftMargin: 5
                            onClickButton: {
                                imagefileDialog.open()
                            }
                        }

                        Text {
                            id:imagepathText
                            text: "请选择影像数据集"
                            font.family: "Verdana"
                            font.pixelSize: 14
                            font.weight: Font.Light
                            anchors.top: parent.top
                            anchors.left: imagebutton.right
                            anchors.bottom: parent.bottom
                            anchors.right: parent.right
                            anchors.leftMargin: 15
                            anchors.rightMargin: 5
                            elide:Text.ElideRight
                            verticalAlignment: Text.AlignVCenter
                            horizontalAlignment: Text.AlignLeft
                        }
                    }
                }

                Item {
                    height: 20
                    anchors.top: imgmrectangle.bottom
                    anchors.left: parent.left
                    anchors.right: parent.right
                    anchors.leftMargin: 5
                    anchors.rightMargin: 5
                    anchors.topMargin: 8
                    Text {
                        id: relhelement
                        text: "相对航高"
                        font.weight: Font.Light
                        font.family: "Verdana"
                        font.pixelSize: 14
                        width: 58
                        anchors.top: parent.top
                        anchors.bottom: parent.bottom
                        anchors.left: parent.left
                        verticalAlignment: Text.AlignVCenter
                    }
                    Text {
                        id: relhText
                        text: "(m)"
                        font.weight: Font.Light
                        font.family: "Times New Roman"
                        font.pixelSize: 12
                        width: 50
                        anchors.left: relhelement.right
                        anchors.top: parent.top
                        anchors.bottom: parent.bottom
                        verticalAlignment: Text.AlignVCenter
                        horizontalAlignment: Text.AlignLeft
                    }
                    Rectangle {
                        border.width: 1
                        border.color: "#DCDFE6"
                        width: parent.width - relhelement.width - relhText.width
                        anchors.top: parent.top
                        anchors.bottom: parent.bottom
                        anchors.right: parent.right
                        WTextInput{
                            id: relhTextInput
                            placeholder:"请输入相对航高"
                            anchors.fill: parent
                            validator: DoubleValidator { bottom: 0.0}
                        }
                    }
                }

                Item {
                    id:mitem
                    height: 20
                    anchors.bottom: parent.bottom
                    anchors.left: parent.left
                    anchors.right: parent.right
                    anchors.leftMargin: 5
                    anchors.rightMargin: 5
                    anchors.bottomMargin: 8

                    WButton {
                        id: matchingbutton
                        text: "数据匹配表"
                        width: 98
                        anchors.left: parent.left
                        anchors.top: parent.top
                        anchors.bottom: parent.bottom
                        //anchors.topMargin: 1
                        //anchors.bottomMargin: 1
                        anchors.verticalCenter: parent.verticalCenter
                        onClickButton: {
                            matchingfileDialog.open()
                        }
                    }

                    Rectangle{
                        anchors.top: parent.top
                        anchors.bottom: parent.bottom
                        anchors.left: matchingbutton.right
                        anchors.right: parent.right
                        anchors.leftMargin: 10
                        color: "#C5C8CE"
                        Text {
                            id:matchingText
                            text:"请选择影像数据匹配表(格式如:CamA0001,CamB0001,CamV0001)"
                            font.family: "Verdana"
                            font.pixelSize: 12
                            font.weight: Font.Light
                            anchors.fill: parent
                            anchors.leftMargin: 5
                            anchors.rightMargin: 5
                            elide:Text.ElideRight
                            color: "#757575"
                            verticalAlignment: Text.AlignVCenter
                            horizontalAlignment: Text.AlignLeft
                        }
                    }
                }
            }

            Rectangle {
                id:setrectangle
                height: 191
                width: parent.width
                border.width: 1
                border.color: "#DCDFE6"
                anchors.top: frectangle.bottom
                anchors.left: parent.left
                anchors.right: parent.right
                anchors.topMargin: 10
                anchors.leftMargin: 5
                anchors.rightMargin: 5
                Item {
                    id:describeritem
                    width:parent.width
                    height: 28
                    anchors.top: parent.top
                    anchors.topMargin:  5
                    Item{
                        width: parent.width / 2
                        height: parent.height
                        Text {
                            id: describerelement
                            text: "特征数量"
                            font.weight: Font.Light
                            font.family: "Verdana"
                            font.pixelSize: 13
                            width: 55
                            anchors.top: parent.top
                            anchors.bottom: parent.bottom
                            anchors.left: parent.left
                            anchors.leftMargin: 5
                            verticalAlignment: Text.AlignVCenter
                        }
                        ComboBox {
                            id: describerComboBox
                            height: parent.height
                            currentIndex:2
                            font.family: "Verdana"
                            font.pixelSize: 13
                            anchors.left: describerelement.right
                            anchors.right: parent.right
                            anchors.leftMargin: 5
                            anchors.rightMargin: 5
                            enabled:false

                            model: ListModel {
                                ListElement { text: "Low(约5000特征数)" }
                                ListElement { text: "Medium(约10000特征数)" }
                                ListElement { text: "一般(约20000特征数)" }
                                ListElement { text: "High(约50000特征数)" }
                                ListElement { text: "Ultra(约100000特征数)" }
                            }
                        }
                    }

                    Item{
                        width: parent.width / 2
                        height: parent.height
                        anchors.right: parent.right
                        Text {
                            id: qualityelement
                            text: "特征质量"
                            font.weight: Font.Light
                            font.family: "Verdana"
                            font.pixelSize: 13
                            width: 55
                            anchors.top: parent.top
                            anchors.bottom: parent.bottom
                            anchors.left: parent.left
                            anchors.leftMargin: 5
                            verticalAlignment: Text.AlignVCenter
                        }
                        ComboBox {
                            id: qualityComboBox
                            height: parent.height
                            currentIndex:2
                            font.family: "Verdana"
                            font.pixelSize: 13
                            anchors.left: qualityelement.right
                            anchors.right: parent.right
                            anchors.leftMargin: 5
                            anchors.rightMargin: 5
                            enabled:false

                            model: ListModel {
                                ListElement { text: "Low(速度非常快)" }
                                ListElement { text: "Medium(速度快)" }
                                ListElement { text: "一般(速度一般)" }
                                ListElement { text: "High(速度慢)" }
                                ListElement { text: "Ultra(速度非常慢)" }
                            }
                        }
                    }

                }

                Item {
                    id:matchingitem
                    width:parent.width
                    height: 28
                    anchors.top: describeritem.bottom
                    anchors.topMargin:  10

                    Item{
                        width: parent.width / 2
                        height: parent.height
                        Text {
                            id: matchingelement
                            text: "匹配方法"
                            font.weight: Font.Light
                            font.family: "Verdana"
                            font.pixelSize: 13
                            width: 55
                            anchors.top: parent.top
                            anchors.bottom: parent.bottom
                            anchors.left: parent.left
                            anchors.leftMargin: 5
                            verticalAlignment: Text.AlignVCenter
                        }
                        ComboBox {
                            id: matchingComboBox
                            height: parent.height
                            currentIndex: 2
                            font.family: "Verdana"
                            font.pixelSize: 13
                            anchors.left: matchingelement.right
                            anchors.right: parent.right
                            anchors.leftMargin: 5
                            anchors.rightMargin: 5
                            enabled:false

                            model: ListModel {
                                //ListElement { text: "Brute Force" }
                                ListElement { text: "ANN" }
                                ListElement { text: "Cascade Hashing" }
                                ListElement { text: "快速级联哈希" }
                            }
                        }
                    }

                    Item{
                        width: parent.width / 2
                        height: parent.height
                        anchors.right: parent.right
                        Text {
                            id: crosselement
                            text: "是否交叉匹配"
                            font.weight: Font.Light
                            font.family: "Verdana"
                            font.pixelSize: 13
                            width: 80
                            anchors.top: parent.top
                            anchors.bottom: parent.bottom
                            anchors.left: parent.left
                            anchors.leftMargin: 5
                            verticalAlignment: Text.AlignVCenter
                        }
                        CheckBox{
                            id:crossCheckBox
                            height: 18
                            width: 18
                            indicator.width: 18
                            indicator.height: 18
                            checked: false
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.left: crosselement.right
                            anchors.leftMargin: 10
                        }
                    }
                }

               Item {
                    id:coloritem
                    width:parent.width
                    height: 28
                    anchors.top: matchingitem.bottom
                    anchors.topMargin:  10

                    Item{
                        width: parent.width / 2
                        height: parent.height
                        Text {
                            id: colorelement
                            text: "匀色方法"
                            font.weight: Font.Light
                            font.family: "Verdana"
                            font.pixelSize: 13
                            width: 55
                            anchors.top: parent.top
                            anchors.bottom: parent.bottom
                            anchors.left: parent.left
                            anchors.leftMargin: 5
                            verticalAlignment: Text.AlignVCenter
                        }
                        ComboBox {
                            id: colorComboBox
                            height: parent.height
                            currentIndex: 2
                            font.family: "Verdana"
                            font.pixelSize: 13
                            anchors.left: colorelement.right
                            anchors.right: parent.right
                            anchors.leftMargin: 5
                            anchors.rightMargin: 5
                            enabled:false

                            model: ListModel {
                                ListElement { text: "Original Color" }
                                ListElement { text: "Gray World" }
                                ListElement { text: "直方图" }
                            }
                        }
                    }

                    Item{
                        width: parent.width / 2
                        height: parent.height
                        anchors.right: parent.right
                        Text {
                            id: distortionelement
                            text: "原始影像是否存在畸变"
                            font.weight: Font.Light
                            font.family: "Verdana"
                            font.pixelSize: 13
                            width: 130
                            anchors.top: parent.top
                            anchors.bottom: parent.bottom
                            anchors.left: parent.left
                            anchors.leftMargin: 5
                            verticalAlignment: Text.AlignVCenter
                        }
                        CheckBox{
                            id:distortionCheckBox
                            height: 18
                            width: 18
                            indicator.width: 18
                            indicator.height: 18
                            checked: true
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.left: distortionelement.right
                            anchors.leftMargin: 10
                            enabled:false
                        }
                    }
                }

               Item {
                    id:interpolationitem
                    width:parent.width
                    height: 28
                    anchors.top: coloritem.bottom
                    anchors.topMargin:  10

                    Item{
                        width: parent.width / 2
                        height: parent.height
                        Text {
                            id: interpolationelement
                            text: "插值方法"
                            font.weight: Font.Light
                            font.family: "Verdana"
                            font.pixelSize: 13
                            width: 55
                            anchors.top: parent.top
                            anchors.bottom: parent.bottom
                            anchors.left: parent.left
                            anchors.leftMargin: 5
                            verticalAlignment: Text.AlignVCenter
                        }
                        ComboBox {
                            id: interpolationComboBox
                            height: parent.height
                            currentIndex: 2
                            font.family: "Verdana"
                            font.pixelSize: 13
                            anchors.left: interpolationelement.right
                            anchors.right: parent.right
                            anchors.leftMargin: 5
                            anchors.rightMargin: 5
                            enabled:false

                            model: ListModel {
                                ListElement { text: "Nearest Neighbor" }
                                ListElement { text: "Bilinear" }
                                ListElement { text: "三次卷积" }
                            }
                        }
                    }

                    Item{
                        width: parent.width / 2
                        height: parent.height
                        anchors.right: parent.right
                        Text {
                            id: forceelement
                            text: "是否强制拼接"
                            font.weight: Font.Light
                            font.family: "Verdana"
                            font.pixelSize: 13
                            width: 80
                            anchors.top: parent.top
                            anchors.bottom: parent.bottom
                            anchors.left: parent.left
                            anchors.leftMargin: 5
                            verticalAlignment: Text.AlignVCenter
                        }
                        CheckBox{
                            id:forceCheckBox
                            height: 18
                            width: 18
                            indicator.width: 18
                            indicator.height: 18
                            checked: false
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.left: forceelement.right
                            anchors.leftMargin: 10
                        }
                    }
                }

                Item {
                    id:otheritem
                    width:parent.width
                    height: 28
                    anchors.top: interpolationitem.bottom
                    anchors.topMargin: 10
                    Item{
                        width: parent.width / 2
                        height: parent.height
                        Text {
                            id: cpuelement
                            text: "线程数量"
                            font.weight: Font.Light
                            font.family: "Verdana"
                            font.pixelSize: 13
                            width: 55
                            anchors.top: parent.top
                            anchors.bottom: parent.bottom
                            anchors.left: parent.left
                            anchors.leftMargin: 5
                            verticalAlignment: Text.AlignVCenter
                        }
                        ComboBox {
                            id: cpuComboBox
                            height: parent.height
                            font.family: "Verdana"
                            font.pixelSize: 13
                            currentIndex: 0
                            anchors.left: cpuelement.right
                            anchors.right: parent.right
                            anchors.leftMargin: 5
                            anchors.rightMargin: 5

                            model: ListModel {
                                id:cpuComboBoxModel
                                ListElement { text: "请选择线程数" }
                            }
                        }
                    }

                    Item{
                        id:gpuCheckItem
                        width: parent.width / 2
                        height: parent.height
                        anchors.right: parent.right
                        Text {
                            id: gpuelement
                            text: "是否使用GPU"
                            font.weight: Font.Light
                            font.family: "Verdana"
                            font.pixelSize: 13
                            width: 80
                            anchors.top: parent.top
                            anchors.bottom: parent.bottom
                            anchors.left: parent.left
                            anchors.leftMargin: 5
                            verticalAlignment: Text.AlignVCenter
                        }
                        CheckBox{
                            id:gpuCheckBox
                            height: 18
                            width: 18
                            indicator.width: 18
                            indicator.height: 18
                            checked: false
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.left: gpuelement.right
                            anchors.leftMargin: 10

                            onClicked:{
                                if(gpuCheckBox.checked){
                                    var gpuInfo = imgStitch.gpuSupport();
                                    gpuText.text = gpuInfo["version"];
                                }else{
                                    gpuText.text = "";
                                }
                            }

                         Text {
                            id: gpuText
                            font.weight: Font.Light
                            font.family: "Verdana"
                            font.pixelSize: 12
                            color: "#757575"
                            width:180
                            elide: Text.ElideRight
                            anchors.top: parent.top
                            anchors.bottom: parent.bottom
                            anchors.left: gpuCheckBox.right
                            anchors.leftMargin: 10
                            verticalAlignment: Text.AlignVCenter
                            horizontalAlignment:Text.AlignLeft
                         }

                        }
                    }
                }
            }

            Rectangle {
                id: outrectangle
                height: 90
                width: parent.width
                border.width: 1
                border.color: "#DCDFE6"
                anchors.top: setrectangle.bottom
                anchors.left: parent.left
                anchors.right: parent.right
                anchors.topMargin: 10
                anchors.leftMargin: 5
                anchors.rightMargin: 5

                Item{
                    anchors.fill: parent
                    anchors.topMargin: 5
                    anchors.leftMargin: 5
                    anchors.rightMargin: 5
                    anchors.bottomMargin: 5

                    Item{
                        id:fitem
                        width: parent.width
                        height: 20

                        Item {
                            anchors.fill: parent
                            Text {
                                id: felement
                                text: "焦距"
                                font.weight: Font.Light
                                font.family: "Verdana"
                                font.pixelSize: 14
                                width: 30
                                anchors.top: parent.top
                                anchors.bottom: parent.bottom
                                anchors.left: parent.left
                                verticalAlignment: Text.AlignVCenter
                            }
                            Text {
                                id: fmelement
                                text: "(mm)"
                                font.weight: Font.Light
                                font.family: "Times New Roman"
                                font.pixelSize: 12
                                width: 50
                                anchors.left: felement.right
                                anchors.top: parent.top
                                anchors.bottom: parent.bottom
                                verticalAlignment: Text.AlignVCenter
                                horizontalAlignment: Text.AlignLeft
                            }
                            Rectangle {
                                border.width: 1
                                border.color: "#DCDFE6"
                                width: parent.width - felement.width - fmelement.width
                                anchors.top: parent.top
                                anchors.bottom: parent.bottom
                                anchors.right: parent.right
                                WTextInput{
                                    id: fTextInput
                                    placeholder:"请输入焦距"
                                    anchors.fill: parent
                                    validator: DoubleValidator { bottom: 0.0}
                                }
                            }
                        }
                    }

                    Item{
                        id:pitem
                        width: parent.width
                        height: 20
                        anchors.top: fitem.bottom
                        anchors.topMargin: 10
                        Item {
                            anchors.fill: parent
                            Text {
                                id: pelement
                                text: "像素"
                                font.weight: Font.Light
                                font.family: "Verdana"
                                font.pixelSize: 14
                                width: 30
                                anchors.top: parent.top
                                anchors.bottom: parent.bottom
                                verticalAlignment: Text.AlignVCenter
                            }
                            Text {
                                id: pmelement
                                text: "(μm)"
                                font.weight: Font.Light
                                font.family: "Times New Roman"
                                font.pixelSize: 12
                                width: 50
                                anchors.left: pelement.right
                                anchors.top: parent.top
                                anchors.bottom: parent.bottom
                                verticalAlignment: Text.AlignVCenter
                                horizontalAlignment: Text.AlignLeft
                            }
                            Rectangle{
                                border.width: 1
                                border.color: "#DCDFE6"
                                width: parent.width - pelement.width - pmelement.width
                                anchors.top: parent.top
                                anchors.bottom: parent.bottom
                                anchors.right: parent.right
                                WTextInput{
                                    id: pixelTextInput
                                    placeholder:"请输入像素"
                                    anchors.fill: parent
                                    validator: DoubleValidator { bottom: 0.0}
                                }
                            }
                        }
                    }

                    Item{
                        id:hwitem
                        width: parent.width
                        height: 20
                        anchors.top: pitem.bottom
                        anchors.topMargin: 10
                        Item {
                            anchors.fill: parent
                            Text {
                                id: hwelement
                                text: "像幅"
                                font.weight: Font.Light
                                font.family: "Verdana"
                                font.pixelSize: 14
                                width: 30
                                anchors.top: parent.top
                                anchors.bottom: parent.bottom
                                verticalAlignment: Text.AlignVCenter
                            }
                            Text {
                                id: hwmelement
                                text: ""
                                font.weight: Font.Light
                                font.family: "Times New Roman"
                                font.pixelSize: 12
                                width: 50
                                anchors.left: hwelement.right
                                anchors.top: parent.top
                                anchors.bottom: parent.bottom
                                verticalAlignment: Text.AlignVCenter
                                horizontalAlignment: Text.AlignLeft
                            }
                            Rectangle{
                                id:hrectangle
                                width: (parent.width - hwelement.width - hwmelement.width - ielement.width) / 2
                                border.width: 1
                                border.color: "#DCDFE6"
                                anchors.top: parent.top
                                anchors.bottom: parent.bottom
                                anchors.left: hwmelement.right
                                WTextInput{
                                    id: heightTextInput
                                    placeholder:"请输入高像素数"
                                    anchors.fill: parent
                                    validator: DoubleValidator { bottom: 0.0}
                                }
                            }
                            Text {
                                id: ielement
                                text: "-"
                                font.weight: Font.Light
                                font.family: "Verdana"
                                font.pixelSize: 14
                                width: 30
                                anchors.top: parent.top
                                anchors.bottom: parent.bottom
                                anchors.left: hrectangle.right
                                verticalAlignment: Text.AlignVCenter
                                horizontalAlignment: Text.AlignHCenter
                            }
                            Rectangle{
                                id:wrectangle
                                width: (parent.width - hwelement.width - hwmelement.width - ielement.width) / 2
                                border.width: 1
                                border.color: "#DCDFE6"
                                anchors.top: parent.top
                                anchors.bottom: parent.bottom
                                anchors.right: parent.right

                                WTextInput{
                                    id: widthTextInput
                                    placeholder:"请输入宽像素数"
                                    anchors.fill: parent
                                    validator: DoubleValidator { bottom: 0.0}
                                }
                            }
                        }
                    }
                }
            }
        }

        Item {
            id:outpathitem
            height: 28
            anchors.top: rectangle.bottom
            anchors.left: parent.left
            anchors.right: parent.right
            anchors.topMargin: 10
            anchors.rightMargin: 5

            WButton {
                id: outpathbutton
                text:"输出目录"
                font.family: "Verdana"
                width: 100
                anchors.left: parent.left
                anchors.top: parent.top
                anchors.bottom: parent.bottom
                anchors.topMargin: 2
                anchors.bottomMargin: 2
                anchors.leftMargin: 5
                onClickButton: {
                    outpathfileDialog.open()
                }
            }

            Rectangle{
                anchors.bottom: parent.bottom
                anchors.top: parent.top
                anchors.left: outpathbutton.right
                anchors.right: parent.right
                anchors.bottomMargin: 0
                anchors.topMargin: 0
                anchors.leftMargin: 10
                color: "#C5C8CE"
                Text {
                    id: outpathText
                    text:"请选择输出目录"
                    opacity: 1
                    anchors.fill: parent
                    anchors.leftMargin: 5
                    anchors.rightMargin: 5
                    elide:Text.ElideRight
                    verticalAlignment: Text.AlignVCenter
                    horizontalAlignment: Text.AlignLeft
                    font.pixelSize: 14
                    font.family: "Verdana"
                    color: "#757575"
                }
            }
        }

        Rectangle{
            width: parent.width
            anchors.top: outpathitem.bottom
            anchors.bottom: parent.bottom
            anchors.topMargin: 10
            color: "#F0F0F0"
            border.color: "#DFDFDF"
            border.width: 1

            WButton {
                id: cancelbutton
                text:"取消"
                font.family: "Verdana"
                width: 85
                height: 25
                anchors.right: parent.right
                anchors.verticalCenter: parent.verticalCenter
                anchors.rightMargin: 5
                onClickButton:Qt.quit()
            }

            WButton {
                id: computebutton
                text:"确定"
                font.family: "Verdana"
                width: 85
                height: 25
                anchors.right: cancelbutton.left
                anchors.verticalCenter: parent.verticalCenter
                anchors.rightMargin: 10
                borderWidth:2
                hoverBorderWidth:2

                onClickButton: {
                    var platformfile = platformtext.text;
                    var relheight = relhTextInput.text;
                    var matchingfile = matchingText.text;
                    var describerIndex = describerComboBox.currentIndex;
                    var qualityIndex = qualityComboBox.currentIndex;
                    var matchingIndex = matchingComboBox.currentIndex;
                    var interpolationIndex = interpolationComboBox.currentIndex;
                    var colorIndex = colorComboBox.currentIndex;
                    var threads = cpuComboBox.currentIndex;
                    var distortion = distortionCheckBox.checked;
                    var cross =  crossCheckBox.checked;
                    var gpu = gpuCheckBox.checked;
                    var force = forceCheckBox.checked;
                    var f = fTextInput.text;
                    var pixel = pixelTextInput.text;
                    var height = heightTextInput.text;
                    var width = widthTextInput.text;
                    var outpath = outpathText.text;
                    
                    var parameter = {
                        platformfile,
                        relheight,
                        matchingfile,
                        describerIndex,
                        qualityIndex,
                        matchingIndex,
                        interpolationIndex,
                        colorIndex,
                        threads,
                        distortion,
                        cross,
                        gpu,
                        force,
                        f,
                        pixel,
                        height,
                        width,
                        outpath,
                        platform:imgstitch.platform
                    }

                    imgStitch.sure(parameter);
                }
            }
        }
    }

    FileDialog {
         id: platformfileDialog
         title: "请选择平台参数文件"
         nameFilters: [ "平台参数文件 (*.wml)"]
         folder: "file:///" +  getFileFolder(platformtext.text)
         onAccepted: {
             platformtext.text = removeFilePrefix(platformfileDialog.fileUrl.toString());
             var platform = imgStitch.readPlatformFile(platformtext.text);
             imgstitch.platform = {};

             cameraComboBoxModel.clear();
             cameraComboBoxModel.append({text:"请选择相机", value:"-1"});
             for (var i = 0; i < platform.length; i++){
                 imgstitch.platform[platform[i]["id"]] = platform[i];
                 cameraComboBoxModel.append({text:platform[i]["idText"],value:platform[i]["id"]});
             }
             comboBox.currentIndex = 1;
         }
     }

    FileDialog {
         id: imagefileDialog
         title: "请选择影像数据集"
         selectFolder: true
         folder: "file:///" + imagepathText.text;
         onAccepted: {
             imagepathText.text = removeFilePrefix(imagefileDialog.folder.toString());
             var currentIndex = comboBox.currentIndex;
             if(currentIndex - 1 >= 0){
                imgstitch.platform[(currentIndex - 1).toString()]["imagePath"] = imagepathText.text;
             }
         }
     }

    FileDialog {
         id: matchingfileDialog
         title: "请选择数据匹配表"
         nameFilters: [ "数据匹配表 (*.csv)"]
         folder: "file:///" +  getFileFolder(matchingText.text)
         onAccepted: {
             matchingText.text = removeFilePrefix(matchingfileDialog.fileUrl.toString())
         }
     }

    FileDialog {
         id: outpathfileDialog
         title: "请选择输出目录"
         selectFolder: true
         folder: "file:///" + outpathText.text;
         onAccepted: {
             outpathText.text = removeFilePrefix(outpathfileDialog.folder.toString())
         }
     }

    Dialog {
          id: progressDialog
          height: 40
          width: 400   
          modality: Qt.WindowModal
          title: "影像拼接中···"
          contentItem:Rectangle{
              id:progressrectangle
              anchors.fill: parent
              ProgressBar {
                 id:progressbar
                 anchors.fill: parent
                 anchors.margins: 5
                 value: 0
                 contentItem: Rectangle {
                     //implicitWidth: parent.width
                     //implicitHeight: parent.height
                     anchors.fill: parent
                     property double provalue: parent.value
                     Rectangle {
                            width: parent.width * parent.provalue
                            height: parent.height
                            color: "#19BE6B"
                    }
                     Text {
                         id: progresstext
                         text: (parent.provalue * 100).toFixed(1) + "%"
                         font.weight: Font.Light
                         font.family: "Verdana"
                         font.pixelSize: 13
                         anchors.centerIn: parent
                         verticalAlignment: Text.AlignVCenter
                         horizontalAlignment: Text.AlignHCenter
                     }
                 }
              }
          }
    }

   WDialog{
        id: errorDialog
        title: "错误"
        imgSource:"qrc:/image/error.png"
        onClickSureButton:{
            errorDialog.close();
        }
    }

   WDialog{
        id: infoDialog
        title: "提示"
        imgSource:"qrc:/image/info.png"
        onClickSureButton:{
            infoDialog.close();
        }
    }

   function removeFilePrefix(path) {
        if (path.startsWith("file:///")) {
            return path.substring(8);
        } else {
            return path;
        }
    }

    function getFileFolder(path) {
        if (path !== "" && path !== null && path !== undefined) {
            return path.split('/').slice(0, -1).join('/');
        } else {
            return "";
        }
    }

     Settings {
        id: parameterSettings
        property var relheight:""
        property string platformfile: "请选择平台参数文件"
        property string matchingfile: "请选择影像数据匹配表"
        property int describerIndex:0
        property int qualityIndex:2
        property int matchingIndex:2
        property int colorIndex:0
        property int interpolationIndex:1
        property int cpuIndex:0
        property bool distortion:true
        property bool cross:false
        property bool gpu:true
        property string gpuName:""
        property var f:""
        property var pixel:""
        property var height:""
        property var width:""
        property string outpath:"请选择输出目录"
        property var platform
    }

    Component.onCompleted: {
        if(parameterSettings.relheight !== "") relhTextInput.text = parameterSettings.relheight;
        if(parameterSettings.platformfile !== "") platformtext.text = parameterSettings.platformfile;
        if(parameterSettings.matchingfile !== "") matchingText.text = parameterSettings.matchingfile;
        if(parameterSettings.cross !== "") crossCheckBox.checked = parameterSettings.cross;
        if(parameterSettings.gpu) {
            var gpuInfo = imgStitch.gpuSupport();
            gpuCheckBox.checked = gpuInfo["support"];
            gpuText.text = gpuInfo["version"];
        }
        if(parameterSettings.distortion !== "") distortionCheckBox.checked = parameterSettings.distortion;
        if(parameterSettings.f !== "") fTextInput.text = parameterSettings.f;
        if(parameterSettings.pixel !== "") pixelTextInput.text = parameterSettings.pixel;
        if(parameterSettings.height !== "") heightTextInput.text = parameterSettings.height;
        if(parameterSettings.width !== "") widthTextInput.text = parameterSettings.width;
        if(parameterSettings.outpath !== "") outpathText.text = parameterSettings.outpath;
        
        var cpus = imgStitch.getThreads();

        cpuComboBoxModel.clear();
        cpuComboBoxModel.append({text:"请选择线程数"});

        for(var i = 0;i < cpus;i++){
            cpuComboBoxModel.append({text:(i+1).toString()});
        }

        if(parameterSettings.cpuIndex === 0) {
            cpuComboBox.currentIndex = cpus / 2.0;
        }else{
            cpuComboBox.currentIndex = parameterSettings.cpuIndex;
        }

        if(parameterSettings.platform){
            var platform = parameterSettings.platform;
             imgstitch.platform = platform;
             //for(var k = cameraComboBoxModel.count - 1; k > 0; k--){
             //    cameraComboBoxModel.remove(k);
             //}

            cameraComboBoxModel.clear();
            cameraComboBoxModel.append({text:"请选择相机", value:"-1"});

            for(var i in platform){
               cameraComboBoxModel.append({text:platform[i]["idText"],value:platform[i]["id"]});
            }
            comboBox.currentIndex = 1;
        }
    }

    Component.onDestruction:{
          parameterSettings.relheight = relhTextInput.text;
          parameterSettings.platformfile = platformtext.text;
          parameterSettings.matchingfile = matchingText.text;
          parameterSettings.describerIndex = describerComboBox.currentIndex;
          parameterSettings.qualityIndex = qualityComboBox.currentIndex;
          parameterSettings.matchingIndex = matchingComboBox.currentIndex;
          parameterSettings.interpolationIndex = interpolationComboBox.currentIndex;
          parameterSettings.cpuIndex = cpuComboBox.currentIndex;
          parameterSettings.colorIndex = colorComboBox.currentIndex;
          parameterSettings.distortion = distortionCheckBox.checked;
          parameterSettings.cross = crossCheckBox.checked;
          parameterSettings.gpu = gpuCheckBox.checked;
          parameterSettings.f = fTextInput.text;
          parameterSettings.pixel = pixelTextInput.text;
          parameterSettings.height = heightTextInput.text;
          parameterSettings.width = widthTextInput.text;
          parameterSettings.outpath = outpathText.text;
          parameterSettings.platform = imgstitch.platform;
    }

   Connections {
        target: imgStitch
        onProgress: {
            progressbar.value = value;
        }
        onStart:{
            if(value === 0) progressbar.value = 0;
            progressDialog.open();
        }
        onFinish:{
            infoDialog.text = value;
            infoDialog.open();
        }
        onError:{
            errorDialog.text = value;
            errorDialog.open();
        }
    }
}