/****************************************************************************
** Meta object code from reading C++ file 'imgStitch.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.15.2)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include <memory>
#include "../../../imgStitch.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'imgStitch.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.15.2. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_imgStitch_t {
    QByteArrayData data[13];
    char stringdata0[107];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_imgStitch_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_imgStitch_t qt_meta_stringdata_imgStitch = {
    {
QT_MOC_LITERAL(0, 0, 9), // "imgStitch"
QT_MOC_LITERAL(1, 10, 8), // "progress"
QT_MOC_LITERAL(2, 19, 0), // ""
QT_MOC_LITERAL(3, 20, 5), // "value"
QT_MOC_LITERAL(4, 26, 5), // "start"
QT_MOC_LITERAL(5, 32, 6), // "finish"
QT_MOC_LITERAL(6, 39, 5), // "error"
QT_MOC_LITERAL(7, 45, 16), // "readPlatformFile"
QT_MOC_LITERAL(8, 62, 8), // "filePath"
QT_MOC_LITERAL(9, 71, 4), // "sure"
QT_MOC_LITERAL(10, 76, 8), // "jsObject"
QT_MOC_LITERAL(11, 85, 10), // "gpuSupport"
QT_MOC_LITERAL(12, 96, 10) // "getThreads"

    },
    "imgStitch\0progress\0\0value\0start\0finish\0"
    "error\0readPlatformFile\0filePath\0sure\0"
    "jsObject\0gpuSupport\0getThreads"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_imgStitch[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
       8,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       4,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   54,    2, 0x06 /* Public */,
       4,    1,   57,    2, 0x06 /* Public */,
       5,    1,   60,    2, 0x06 /* Public */,
       6,    1,   63,    2, 0x06 /* Public */,

 // methods: name, argc, parameters, tag, flags
       7,    1,   66,    2, 0x02 /* Public */,
       9,    1,   69,    2, 0x02 /* Public */,
      11,    0,   72,    2, 0x02 /* Public */,
      12,    0,   73,    2, 0x02 /* Public */,

 // signals: parameters
    QMetaType::Void, QMetaType::Double,    3,
    QMetaType::Void, QMetaType::Int,    3,
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QString,    3,

 // methods: parameters
    QMetaType::QVariantList, QMetaType::QString,    8,
    QMetaType::Void, QMetaType::QJsonObject,   10,
    QMetaType::QVariantMap,
    QMetaType::QVariant,

       0        // eod
};

void imgStitch::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<imgStitch *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->progress((*reinterpret_cast< double(*)>(_a[1]))); break;
        case 1: _t->start((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 2: _t->finish((*reinterpret_cast< QString(*)>(_a[1]))); break;
        case 3: _t->error((*reinterpret_cast< QString(*)>(_a[1]))); break;
        case 4: { QVariantList _r = _t->readPlatformFile((*reinterpret_cast< const QString(*)>(_a[1])));
            if (_a[0]) *reinterpret_cast< QVariantList*>(_a[0]) = std::move(_r); }  break;
        case 5: _t->sure((*reinterpret_cast< const QJsonObject(*)>(_a[1]))); break;
        case 6: { QVariantMap _r = _t->gpuSupport();
            if (_a[0]) *reinterpret_cast< QVariantMap*>(_a[0]) = std::move(_r); }  break;
        case 7: { QVariant _r = _t->getThreads();
            if (_a[0]) *reinterpret_cast< QVariant*>(_a[0]) = std::move(_r); }  break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (imgStitch::*)(double );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&imgStitch::progress)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (imgStitch::*)(int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&imgStitch::start)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (imgStitch::*)(QString );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&imgStitch::finish)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (imgStitch::*)(QString );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&imgStitch::error)) {
                *result = 3;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject imgStitch::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_imgStitch.data,
    qt_meta_data_imgStitch,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *imgStitch::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *imgStitch::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_imgStitch.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int imgStitch::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 8)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 8)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 8;
    }
    return _id;
}

// SIGNAL 0
void imgStitch::progress(double _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void imgStitch::start(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void imgStitch::finish(QString _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void imgStitch::error(QString _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
