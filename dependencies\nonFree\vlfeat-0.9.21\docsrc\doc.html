<!DOCTYPE group PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<group>

<p>The VLFeat <em>reference documentation</em> has three parts:</p>

<table class="boxes threecols">
  <tr>
    <td>
      <h1><a class="plain" href="%pathto:matlab;">MATLAB functions</a></h1>
      <p>The reference documentation of VLFeat MATLAB commands (this
        is an on-line version of the documentation built in the
        command themsevles).</p>
    </td>
    <td>
      <h1><a class="plain" href="%pathto:api;">C API
          reference</a></h1><p>This documentation
        includes <b>descriptions of all the algorithms</b> and hence
        it is useful even if you do not plan to use the C library
        directly.</p>
    </td>
    <td>
      <h1><a class="plain" href="%pathto:man;">Man pages</a></h1>
      <p>The documentation of the command line
        utilities bundled with VLFeat (this is an on-line version
        of the Unix man found in the <code>src/</code>
        subdirectory.</p>
    </td>
  </tr>
</table>

<p>In addition to the documentation, there are also
<a href="%pathto:tut;">tutorials</a> which introduce many of the
algorithms contained in the library.</p>

</group>
