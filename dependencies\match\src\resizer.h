#pragma once
#include <vector>
#include <string>
#include <functional>

namespace SWDC {
	namespace resizer {

		class resizer
		{
		public:
			resizer(std::vector<std::string>& imagePaths, std::string& output):
				_imagePaths(imagePaths), _output(output)
			{

			}

			resizer& setMaxThreads(int maxThreads) noexcept {
				_maxThreads = maxThreads;
				return *this;
			}

			resizer& setHeight(int width) noexcept {
				_width = width;
				return *this;
			}

			resizer& setWidth(int height) noexcept {
				_height = height;
				return *this;
			}

			bool process(std::function<void(double progress, std::string message)> func = nullptr);

			~resizer() {}

		private:
			void resize(std::string& inputPath, std::string& outputPath);

		private:
			int _width = 1000;
			int _height = 1000;
			std::string _output;
			std::vector<std::string> _imagePaths;

			int _maxThreads = 1;
		};

	}
}
