#pragma once
#include <QObject> 
#include <QThread> 
#include <QVariant> 
#include <QFuture>

class imgStitch : public QObject
{
	Q_OBJECT

signals:
	void progress(double value);

	void start(int value);

	void finish(QString value);

	void error(QString value);

private:
	bool isRuning = false;

public:
	explicit imgStitch(QObject* parent = nullptr);

	Q_INVOKABLE QVariantList readPlatformFile(const QString& filePath);

	Q_INVOKABLE void sure(const QJsonObject& jsObject);

	Q_INVOKABLE QVariantMap gpuSupport();

	Q_INVOKABLE QVariant getThreads();


public:

	~imgStitch();
};

