<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="595.276" height="841.89" viewBox="0, 0, 595.276, 841.89">
  <g id="Layer 1">
    <g>
      <path d="M198.925,220.185 L342.535,220.185 L342.535,361.917 L198.925,361.917 z" fill="#FFFFFF"/>
      <path d="M198.925,220.185 L342.535,220.185 L342.535,361.917 L198.925,361.917 z" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    </g>
    <g>
      <path d="M43.02,57.193 L213.098,57.193 L213.098,184.752 L43.02,184.752 z" fill="#FFFFFF"/>
      <path d="M43.02,57.193 L213.098,57.193 L213.098,184.752 L43.02,184.752 z" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    </g>
    <g>
      <path d="M312.311,57.193 L482.39,57.193 L482.39,184.752 L312.311,184.752 z" fill="#FFFFFF"/>
      <path d="M312.311,57.193 L482.39,57.193 L482.39,184.752 L312.311,184.752 z" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    </g>
    <path d="M105.462,157.184 L133.588,157.184 C133.588,153.186 133.302,152.393 126.681,152.393 C127.738,149.22 132.038,141.542 134.428,141.542 C136.541,141.542 139.063,141.659 139.063,146.482 C139.063,151.958 148.278,160.786 150.513,157.184 C153.842,151.817 144.788,153.454 144.788,140.508 C144.788,122.798 155.123,125.32 155.123,115.37 C155.123,110.392 153.748,109.72 153.748,106.16 C153.748,101.457 157.892,101.73 157.086,97.83 C156.529,95.136 156.093,92.976 155.768,89.718 C155.538,87.407 155.464,84.943 153.064,85.042 C150.243,85.159 149.173,90.124 144.788,90.48 C140.412,90.835 136.681,86.755 134.988,87.338 C133.308,87.916 133.836,92.471 134.832,95.707 C136.397,100.792 139.81,107.405 133.588,108.401 C127.365,109.396 116.663,110.392 108.947,120.597 C101.231,130.801 101.516,142.777 98.493,146.482 C88.348,158.914 77.953,153.81 77.953,164.154 C77.953,168.784 85.799,172.118 87.044,170.625 C88.288,169.131 76.469,164.682 89.694,159.424 C100.991,154.933 102,154.016 105.462,157.184 z" fill="#D7D4D8"/>
    <g>
      <path d="M145.276,113.386 C135.491,113.386 127.559,105.454 127.559,95.669 C127.559,85.885 135.491,77.953 145.276,77.953 C155.06,77.953 162.992,85.885 162.992,95.669 C162.992,105.454 155.06,113.386 145.276,113.386 z" fill-opacity="0" stroke="#69F903" stroke-width="4"/>
      <path d="M145.276,113.386 C135.491,113.386 127.559,105.454 127.559,95.669 C127.559,85.885 135.491,77.953 145.276,77.953 C155.06,77.953 162.992,85.885 162.992,95.669 C162.992,105.454 155.06,113.386 145.276,113.386 z" fill-opacity="0" stroke="#000000" stroke-width="0.5"/>
    </g>
    <path d="M407.453,153.589 L417.126,142.721 C413.186,139.214 412.307,138.629 410.029,141.187 C407.266,137.995 401.18,129.6 402.001,128.677 C402.729,127.86 403.711,126.988 408.463,131.218 C413.859,136.02 425.728,140.203 422.947,136.18 C418.804,130.186 417.302,135.12 404.546,123.767 C387.095,108.234 393.134,106.452 383.33,97.725 C378.425,93.359 377.289,93.301 373.782,90.179 C369.148,86.054 370.842,84.692 366.722,81.583 C363.876,79.436 361.597,77.71 358.275,74.978 C355.918,73.04 353.466,70.908 352.738,71.922 C351.882,73.114 356.407,77.883 355.249,79.889 C354.094,81.892 348.79,79.755 348.782,80.92 C348.774,82.076 353.445,85.868 356.976,88.321 C362.525,92.176 370.215,96.657 369.056,99.935 C367.897,103.212 365.196,108.221 372.598,120.153 C380,132.085 391.899,142.478 394.51,146.895 C403.271,161.719 394.666,161.259 404.858,170.331 C409.421,174.393 415.405,174.285 414.362,172.494 C413.318,170.703 404.868,171.368 404.237,161.647 C403.697,153.342 403.14,152.148 407.453,153.589 z" fill="#D7D4D8"/>
    <g>
      <path d="M377.988,99.79 C374.623,103.571 364.079,99.68 354.437,91.098 C344.796,82.517 339.708,72.495 343.073,68.714 C346.438,64.933 356.983,68.824 366.624,77.406 C376.266,85.988 381.353,96.009 377.988,99.79 z" fill-opacity="0" stroke="#69F903" stroke-width="4"/>
      <path d="M377.988,99.79 C374.623,103.571 364.079,99.68 354.437,91.098 C344.796,82.517 339.708,72.495 343.073,68.714 C346.438,64.933 356.983,68.824 366.624,77.406 C376.266,85.988 381.353,96.009 377.988,99.79 z" fill-opacity="0" stroke="#000000" stroke-width="0.5"/>
    </g>
    <path d="M318.175,275.474 C325.567,280.139 320.625,287.556 316.471,294.848 C310.617,305.125 307.395,312.395 303.417,321.489 C297.658,334.655 285.831,325.293 275.976,339.164 C272.621,343.649 270.804,348.966 268.895,354.144 C255.246,353.871 248.904,350.128 237.228,344.145 C226.423,337.026 217.837,327.085 212.818,315.182 C211.225,311.406 210.395,307.359 209.183,303.447 L209.165,303.272 L210.954,304.14 C231.66,314.053 235.302,287.505 241.275,269.277 C245.075,257.68 253.04,243.155 259.276,244.918 C265.565,246.696 268.18,266.433 282.018,274.421 C295.885,282.425 309.491,269.992 318.175,275.474 z" fill="#D7D4D8"/>
    <text transform="matrix(1, 0, 0, 1, 265.572, 77.661)">
      <tspan x="-2.503" y="3" font-family="Helvetica" font-size="9" fill="#000000">u</tspan>
    </text>
    <g>
      <path d="M270.23,354.331 C234.487,354.331 205.512,325.776 205.512,290.551 C205.512,255.327 234.487,226.772 270.23,226.772 C305.973,226.772 334.949,255.327 334.949,290.551 C334.949,325.776 305.973,354.331 270.23,354.331 z" fill-opacity="0" stroke="#69F903" stroke-width="4"/>
      <path d="M270.23,354.331 C234.487,354.331 205.512,325.776 205.512,290.551 C205.512,255.327 234.487,226.772 270.23,226.772 C305.973,226.772 334.949,255.327 334.949,290.551 C334.949,325.776 305.973,354.331 270.23,354.331 z" fill-opacity="0" stroke="#000000" stroke-width="0.5"/>
    </g>
    <g>
      <path d="M149.319,99.713 L157.69,106.799" fill-opacity="0" stroke="#69F903" stroke-width="4"/>
      <path d="M149.319,99.713 L157.69,106.799" fill-opacity="0" stroke="#000000" stroke-width="0.5"/>
    </g>
    <g>
      <path d="M361.917,85.539 L372,85.539" fill-opacity="0" stroke="#69F903" stroke-width="4"/>
      <path d="M361.917,85.539 L372,85.539" fill-opacity="0" stroke="#000000" stroke-width="0.5"/>
    </g>
    <g>
      <path d="M-1910.871,-15.587 L-1902.5,-8.5" fill-opacity="0" stroke="#69F903" stroke-width="4"/>
      <path d="M-1910.871,-15.587 L-1902.5,-8.5" fill-opacity="0" stroke="#000000" stroke-width="0.5"/>
    </g>
    <g>
      <path d="M270.73,291.051 L270.73,354.831" fill-opacity="0" stroke="#69F903" stroke-width="4"/>
      <path d="M270.73,291.051 L270.73,354.831" fill-opacity="0" stroke="#000000" stroke-width="0.5"/>
    </g>
    <text transform="matrix(1, 0, 0, 1, 270.23, 212.598)">
      <tspan x="-37.27" y="1.913" font-family="Helvetica" font-size="9" fill="#000000">normalized feature</tspan>
    </text>
    <g>
      <path d="M170.579,91.831 L335.79,85.786" fill-opacity="0" stroke="#0433FF" stroke-width="0.75"/>
      <path d="M335.872,88.035 L341.786,85.567 L335.707,83.538 z" fill="#0433FF" fill-opacity="1" stroke="#0433FF" stroke-width="0.75" stroke-opacity="1"/>
    </g>
    <g>
      <path d="M235.642,227.272 L161.681,126.416" fill-opacity="0" stroke="#0433FF" stroke-width="0.75"/>
      <path d="M163.496,125.085 L158.133,121.577 L159.867,127.746 z" fill="#0433FF" fill-opacity="1" stroke="#0433FF" stroke-width="0.75" stroke-opacity="1"/>
    </g>
    <text transform="matrix(1, 0, 0, 1, 127.559, 49.606)">
      <tspan x="-20.755" y="1.913" font-family="Helvetica" font-size="9" fill="#000000">first image</tspan>
    </text>
    <text transform="matrix(1, 0, 0, 1, 396.85, 49.606)">
      <tspan x="-28.017" y="1.913" font-family="Helvetica" font-size="9" fill="#000000">second image</tspan>
    </text>
    <text transform="matrix(1, 0, 0, 1, 194.882, 154.819)">
      <tspan x="-2.503" y="3" font-family="Helvetica" font-size="9" fill="#000000">u</tspan>
    </text>
    <g>
      <path d="M312.311,227.272 L359.471,106.004" fill-opacity="0" stroke="#0433FF" stroke-width="0.75"/>
      <path d="M361.568,106.819 L361.646,100.412 L357.374,105.188 z" fill="#0433FF" fill-opacity="1" stroke="#0433FF" stroke-width="0.75" stroke-opacity="1"/>
    </g>
    <text transform="matrix(1, 0, 0, 1, 331.405, 154.819)">
      <tspan x="-3.362" y="3" font-family="Helvetica" font-size="9" fill="#000000">u'</tspan>
    </text>
    <text transform="matrix(1, 0, 0, 1, 166.242, 105.213)">
      <tspan x="-3.25" y="3" font-family="Helvetica" font-size="9" fill="#000000">R</tspan>
    </text>
    <text transform="matrix(1, 0, 0, 1, 379.699, 83.953)">
      <tspan x="-4.109" y="3" font-family="Helvetica" font-size="9" fill="#000000">R'</tspan>
    </text>
    <text transform="matrix(1, 0, 0, 1, 297.638, 254.032)">
      <tspan x="-5.752" y="3" font-family="Helvetica" font-size="9" fill="#000000">R0</tspan>
    </text>
  </g>
  <defs/>
</svg>
