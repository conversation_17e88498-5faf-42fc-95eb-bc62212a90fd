<!DOCTYPE group PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<group>

<pagestyle>
#content table.checker {
    border-collapse: collapse ;
    margin-left: auto ;
    margin-right: auto ;
}
#content table.checker td {
    background-color: #f6f6f6 ;
    border: none ;
    border-top: 1px solid #DDD ;
    border-bottom: 1px solid #DDD ;
    padding: 0.5em ;
}
</pagestyle>

<p>These instructions explain how to use VLFeat from the command line
(shell).</p>

<p><a href="%pathto:download;">Download</a> and unpack the latest
VLFeat binary distribution in a directory of your choice
(e.g. <code>~/src/vlfeat</code>). Let <code>VLFEATROOT</code> denote
this directory.</p>

<p>The command line tools are located
in <code>VLFEATROOT/bin/ARCH</code>. Here <code>ARCH</code> denotes
the subdirectory relative to your architecture (e.g. <code>mac</code>
for Mac OS X PPC, <code>maci</code> for Mac OS X
Intel, <code>glnx86</code> for Linux, and so on). For the sake of
illustration, the following table gives the path to the SIFT feature
extraction program for the varius architectures:</p>

<div class="p">
<table class="checker">
<tr>
<th>Platform</th>
<th><code>ARCH</code></th>
<th>Path to command</th>
</tr>
<tr>
<td>Windows 32</td>
<td><code>win32</code></td>
<td><code>VLFEATROOT\bin\w32\sift.exe</code></td>
</tr>
<tr>
<td>Windows 64</td>
<td><code>win64</code></td>
<td><code>VLFEATROOT\bin\w64\sift.exe</code></td>
</tr>
<tr>
<td>Mac Intel 32</td>
<td><code>maci</code></td>
<td><code>VLFEATROOT/bin/maci/sift</code></td>
</tr>
<tr>
<td>Mac Intel 64</td>
<td><code>maci64</code></td>
<td><code>VLFEATROOT/bin/maci64/sift</code></td>
</tr>
<tr>
<td>Linux 32</td>
<td><code>glnx86</code></td>
<td><code>VLFEATROOT/bin/glnx86/sift</code></td>
</tr>
<tr>
<td>Linux 64</td>
<td><code>glnxa64</code></td>
<td><code>VLFEATROOT/bin/glnxa64/sift</code></td>
</tr>
</table>
</div>

<p>All commands have a corresponding man page found
in <code>VLFEATROOT/src</code>.  For UNIX based systems, the man pages
can be viewed with the <code>man</code> utility. For instance
</p>

<precode type='sh'>
> man VLFEATROOT/src/sift.1
</precode>

<p>It might be convenient to add VLFeat to the system search paths. In
Linux and Mac OS X this involves modifying the <code>PATH</code>
and <code>MANPATH</code> environment variables. The exact details may
vary, but it should be enough to add the following to your
<code>~/.bash_profile</code>:</p>

<precode type='sh'>
export PATH=VLFEATROOT/bin/ARCH:$PATH
export MANPATH=VLFEATROOT/src:$MANPATH
</precode>

<p>Alternatively, you can copy the executables and man pages to
appropriate system-wide directories.</p>

</group>
