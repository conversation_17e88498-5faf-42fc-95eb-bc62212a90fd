#pragma once

#include <numeric.hpp>
#include "conditioning.hpp"

namespace SWDC {
	namespace robustEstimation {

		template<typename SolverT_, typename UnnormalizerT_, typename ModelT_ = Mat3>
		class PerspectiveKernel
		{
		public:
			using SolverT = SolverT_;
			using ModelT = ModelT_;

			PerspectiveKernel(
				const Mat& x1, int w1, int h1,
				const Mat& x2, int w2, int h2)
				:_x1n(x1.rows(), x1.cols()),
				_x2n(x2.rows(), x2.cols()),
				_N1(3, 3),
				_N2(3, 3)
			{
				assert(2 == x1.rows());
				assert(x1.rows() == x2.rows());
				assert(x1.cols() == x2.cols());

				normalizePointsFromImageSize(x1, &_x1n, &_N1, w1, h1);
				normalizePointsFromImageSize(x2, &_x2n, &_N2, w2, h2);
			}

			void fit(std::vector<ModelT>& models) const
			{
				_kernelSolver.solve(_x1n, _x2n, models);
			}

			void unnormalize(ModelT* model) const 
			{
				UnnormalizerT_::unnormalize(_N1, _N2, model);
			}

		private:
			Mat _x1n, _x2n;    // image points
			Mat3 _N1, _N2;              // Matrix used to normalize data

			/// two view solver
			const SolverT _kernelSolver{};
		};

	}
}
