<!DOCTYPE group PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<group>

<pagestyle>
img.photo {
  float: left ;
  margin: 0px 13px 0px 0px ;
  width: 100px ;
}
</pagestyle>

%tableofcontents;

<p><b>VLFeat</b> is a cross-platform open source collection of vision
algorithms with a special focus on visual features (for instance SIFT
and MSER) and clustering (k-means, hierarchical k-means, agglomerative
information bottleneck). It bundles a MATLAB toolbox, a clean and
portable C library and a number of command line utilities. Thus it is
possible to use the same algorithm both from MATLAB, the command line,
and your own programs.</p>

<p>Many parties make the development of VLFeat possible:</p>

<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
<h2 id="about.sponsors">Sponsors</h2>
<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->

<p><img src="images/PASCAL2.png" style="height:6em;float:left;"
alt="PASCAL2 credits"></img> In 2012 the development of VLFeat is
supported by the PASCAL Harvest programme. Several people have been
working in Oxford to add new functionalities to the library. Moreover,
leading researchers in computer vision were consulted as
<a href="%pathto:about.adisors">advisors</a> of the project. Some of
these advancements have been presented at a
<a href='https://sites.google.com/site/eccv12features/'>tutorial at
the European Conference in Computer Vision</a> (ECCV) 2012. See
the <a href="%pathto:roadmap;">PASCAL Harvest Roadmap</a> for further
details.</p>

<a shape="rect"
href="http://company.yandex.com/">
<img src="images/yandex.png" style="height:4em;" alt="Yandex
credits"></img></a>

<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
<h2 id="about.team">Team</h2>
<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->

<p><b>VLFeat</b> is developed by a team of computer vision
researchers, including master and PhD students, postgraduates, and
senior researchers, as listed below. VLFeat was created by Andrea
Vedaldi and Brian Fulkerson in 2007, based on previously published
software by the same authors.</p>

<p>
 <img class="photo" src="%pathto:root;images/vedaldi.jpg"
      alt="Andrea Vedaldi picture"/>

 <a href="http://www.vlfeat.org/~vedaldi">Andrea Vedaldi</a> joined
 the faculty at the University of Oxford in 2012. Since 2008 he was
 junior research fellow in the Oxford Visual Geometry Group. He
 received the Ph.D. and Master's degrees in Computer Science from the
 University of California - Los Angeles, in 2008 and 2005, and the
 Bachelor's degree in Information Engineering from the University of
 Padova, Italy, in 2003. He is the recipient of the UCLA outstanding
 Master's and Ph.D. awards.
</p>

<p class="clearfix">
 <img class="photo" src="%pathto:root;images/fulkerson.jpg"
      alt="Brian Fulkerson picture"/>

 <a href="http://uk.linkedin.com/pub/brian-fulkerson/27/449/244">Brian
 Fulkerson</a> received his B.S. in Computer Engineering from the
 University of California - San Diego in 2004, and his M.S. and Ph.D.
 in Computer Science from the University of California - Los Angeles
 Vision Lab in 2006 and 2010.
</p>

<p class="clearfix">
 <img class="photo" src="%pathto:root;images/lenc.jpg"
      alt="Karel Lenc picture"/>

 <b>Karel Lenc</b> received his B.S. degree in Cybernetics and
 Measurements from the Czech Technical University in 2010. He is
 currently pursuing M.S. degree in Computer Vision and Computer
 Engineering at the CTU, Center for Machine Perception. He visited the
 Department of Engineering Mathematics, University of Bristol in 2011
 and the Oxford Visual Geometry Group in 2012.</p>

<p class="clearfix">
 <img class="photo" src="%pathto:root;images/perrone.jpg"
      alt="Daniele Perrone picture"/>

 <a href="http://www.cvg.unibe.ch/daniele%20perrone.html">Daniele
 Perrone</a> received his B.S. degree in Computer Engineering from
 Universit&agrave; della Calabria in 2007, and his M.S. degree in
 Artificial Intelligence from "Sapienza" Universit&agrave; di Roma in
 2009. He started his Ph.D. in June 2010 at Heriot-Watt University,
 Edinburgh, UK. From June 2012 he joined the Computer Vision Group at
 the Universit&auml;t Bern, Switzerland.
</p>

<p class="clearfix">
 <img class="photo" src="%pathto:root;images/perdoch.jpg"
      alt="Michal Perdoch picture"/>

 <a href="http://cmp.felk.cvut.cz/~perdom1/">Michal Perdoch</a>
 received his Bachelor's degree in Software Engineering from the
 Slovak University of Technology in Bratislava in 2001, Master's
 degree in Computer Science and Ph.D. degree in Artificial
 Intelligence and Biocybernetics from the Czech Technical University
 in Prague, in 2004 and 2011. He is currently a postdoctoral
 researcher at the CTU Center for Machine Perception.
</p>

<p class="clearfix">
 <img class="photo" src="%pathto:root;images/sulc.jpg"
      alt="Milan Sulc picture"/>

 <a href="http://cmp.felk.cvut.cz/~sulcmila/">Milan Sulc</a>
 received his Bachelor's degree in Cybernetics and Robotics from the
 Czech Technical University in 2012.
 He is currently pursuing Master degrees in Computer Vision
 and Artificial Intelligence at the CTU
 and Entrepreneurship and Commercial Engineering in Industry at CTU.
 He is a student intern at the CTU Center for Machine Perception.
</p>

<p class="clearfix">
 <img class="photo" src="%pathto:root;images/sarbortova.jpg"
      alt="Hana Sarbortova picture"/>

 <a href="http://cz.linkedin.com/pub/hana-%C5%A1arbortov%C3%A1/65/a15/446">Hana Sarbortova</a>
received her Bachelor of Engineering degree in Digital Signal and Image Processing from the
University of Central Lancashire in 2012. She is currently studying Computer Vision and Digital Image
at the Czech Technical University in Prague. She is a student intern at the CTU Center for Machine Perception.
</p>

<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
<h2 id="about.advisors">Advisors</h2>
<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->

<p>The development of VLFeat is supported by a number of computer
vision groups and researchers:</p>

<ul>
<li>Prof. Andrew
Zisserman, <a href="http://www.robots.ox.ac.uk/~vgg/">Oxford VGG
Lab</a>.</li>
<li>Prof. Jiri Matas, Czech Technical University in Prague.</li>
<li>Prof. Tinne Tuytelaars, KU Leuven.</li>
<li>Dr. Cordelia Schmid, LEAR, Grenoble.</li>
<li>Dr. Krystian Mikolajczyk, Surrey.</li>
<li>Prof. Stefano Soatto, <a href="http://vision.ucla.edu">UCLA Vision
Lab</a>.</li>
</ul>

<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->
<h2 id="about.others">Users and community</h2>
<!-- ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~ -->

<p>The authors would like to thank the many colleagues that have
contributed to VLFeat by testing and providing helpful suggestions and
comments.</p>

</group>
