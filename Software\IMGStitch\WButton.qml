import QtQuick 2.12
import QtQuick.Controls 2.14

Button {
    id: wbutton
    property string backgroundColor: "#E1E1E1"
    property string borderColor:"#ADADAD"
    property int borderWidth: 1
    property string hoverBackgroundColor: "#E5F1FB"
    property string hoverBorderColor: "#0078D7"
    property int hoverBorderWidth: 1
    font.pixelSize: 13
    font.family: "Verdana"
    signal clickButton()
    background: Rectangle {
        id:wbuttonBackground
        color: backgroundColor
        border.color: borderColor
        border.width: borderWidth
    }
    MouseArea {
        anchors.fill: parent
        hoverEnabled: true
        onEntered:{
           wbuttonBackground.color = hoverBackgroundColor;
           wbuttonBackground.border.color = hoverBorderColor;
           wbuttonBackground.border.width = hoverBorderWidth;
        }
        onExited:{
            wbuttonBackground.color = backgroundColor;
            wbuttonBackground.border.color = borderColor;
            wbuttonBackground.border.width = borderWidth;
        }
        onClicked: wbutton.clickButton()
    }
}
